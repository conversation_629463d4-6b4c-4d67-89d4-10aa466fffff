# jq 快速参考指南

## 基本语法

### 基础选择器
```bash
# 获取根级字段
jq '.field' file.json

# 获取嵌套字段
jq '.user.name' file.json

# 获取数组元素
jq '.users[0]' file.json          # 第一个元素
jq '.users[-1]' file.json         # 最后一个元素
jq '.users[1:3]' file.json        # 切片：索引1到2

# 获取所有数组元素
jq '.users[]' file.json

# 获取对象所有值
jq '.user[]' file.json
```

### 常用选项
```bash
jq -r '.field'          # 原始输出（去掉引号）
jq -c '.field'          # 紧凑输出（一行）
jq -n 'expression'      # 不读取输入
jq -s '.[]'             # 将多个JSON合并为数组
jq -e '.field'          # 根据输出设置退出码
jq --arg var value      # 传递变量
```

## 过滤和选择

### select() 函数
```bash
# 基本过滤
jq '.users[] | select(.age > 30)'

# 多条件过滤
jq '.users[] | select(.age > 25 and .active == true)'

# 字符串匹配
jq '.users[] | select(.name | contains("张"))'

# 正则表达式
jq '.users[] | select(.email | test("@gmail.com$"))'
```

### 条件表达式
```bash
# if-then-else
jq '.users[] | if .age > 30 then "老员工" else "年轻员工" end'

# 三元操作符风格
jq '.users[] | (.age > 30) as $old | if $old then "老" else "年轻" end'

# 空值合并
jq '.field // "默认值"'
```

## 数据转换

### map() 函数
```bash
# 转换数组每个元素
jq '.users | map(.name)'

# 复杂转换
jq '.users | map({name, age, city: .address.city})'

# 带条件的map
jq '.users | map(select(.active)) | map(.name)'
```

### 对象构造
```bash
# 创建新对象
jq '{name: .user.name, age: .user.age}'

# 动态键名
jq '{(.key_field): .value_field}'

# 合并对象
jq '. + {new_field: "value"}'
```

## 聚合操作

### 数组操作
```bash
# 数组长度
jq '.users | length'

# 求和
jq '.numbers | add'
jq '[.users[].age] | add'

# 最大/最小值
jq '.numbers | max'
jq '.numbers | min'

# 排序
jq '.users | sort_by(.age)'

# 去重
jq '.items | unique'

# 分组
jq '.users | group_by(.city)'
```

### 统计函数
```bash
# 计数
jq '.users | map(select(.active)) | length'

# 平均值
jq '[.users[].age] | add / length'

# 分组统计
jq '.users | group_by(.city) | map({city: .[0].city, count: length})'
```

## 高级功能

### 自定义函数
```bash
jq '
def avg(stream): 
  [stream] | add / length;

def user_summary(user):
  {name: user.name, age_group: (if user.age > 30 then "senior" else "junior" end)};

.users | map(user_summary(.))
'
```

### 递归操作
```bash
# 递归查找所有字符串
jq '
def find_strings: 
  if type == "object" then [.[] | find_strings] | flatten
  elif type == "array" then [.[] | find_strings] | flatten  
  elif type == "string" then [.]
  else [] end;

find_strings
'
```

### 错误处理
```bash
# try-catch
jq 'try .field.subfield catch "字段不存在"'

# 可选操作符
jq '.field?.subfield?'

# 默认值
jq '.field // "默认值"'
```

## 实用技巧

### 调试
```bash
# 调试输出
jq '.users[] | debug | select(.age > 30)'

# 查看数据结构
jq '.users[0] | keys'
jq '.users[0] | type'
```

### 性能优化
```bash
# 流式处理大文件
jq -c '.users[]' large.json | while read line; do
    echo "$line" | jq '.name'
done

# 避免重复解析
eval $(jq -r '@sh "name=\(.name) age=\(.age)"' data.json)
```

### 多文件处理
```bash
# 合并多个JSON文件
jq -s '.[0] + .[1]' file1.json file2.json

# 处理多个文件
jq -s 'map(.users) | flatten' *.json
```

## 常用模式

### 数据清洗
```bash
# 删除null值
jq 'del(.[] | nulls)'

# 删除空字符串
jq 'del(.[] | select(. == ""))'

# 标准化数据
jq '.users | map(.name |= ascii_downcase)'
```

### 数据验证
```bash
# 检查必需字段
jq '.users[] | select(has("name") and has("email"))'

# 类型验证
jq '.users[] | select((.age | type) == "number")'

# 格式验证
jq '.users[] | select(.email | test("@"))'
```

### 报告生成
```bash
# 生成摘要报告
jq '{
  total_users: (.users | length),
  active_users: (.users | map(select(.active)) | length),
  avg_age: ([.users[].age] | add / length),
  cities: (.users | map(.address.city) | unique)
}'
```

## 常见错误和解决方案

### 类型错误
```bash
# ❌ 错误：字符串和数字比较
jq '.age > "30"'

# ✅ 正确：确保类型一致
jq '.age > 30'
jq '(.age | tonumber) > 30'
```

### 空值处理
```bash
# ❌ 错误：未处理null
jq '.users[].score | add'

# ✅ 正确：过滤null
jq '[.users[].score | select(. != null)] | add'
```

### 数组访问
```bash
# ❌ 错误：假设数组有元素
jq '.users[0].name'

# ✅ 正确：安全访问
jq '.users[0]?.name // "无数据"'
```

这个快速参考指南涵盖了jq的主要功能和常用模式，可以作为日常使用的备忘录。
