#!/bin/bash
# 原始数据收集模块
# 功能: 收集各种检测命令的原始输出数据
# 版本: 1.0

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

# 引入SSH工具函数
if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    echo "错误: 无法找到SSH工具函数库 lib/ssh_utils.sh"
    exit 1
fi

# =============================================================================
# 原始数据收集函数
# =============================================================================

# 收集硬件相关的原始命令输出
collect_raw_hardware_data() {
    local node="$1"
    local host="$2"
    local user="$3"
    local password="$4"
    local port="$5"
    local raw_data_dir="$6"

    local raw_hw_file="$raw_data_dir/hardware_${node}.txt"

    cat >"$raw_hw_file" <<'EOF'
# 主板信息
EOF

    if [[ "$node" == "localhost" ]]; then
        # 本地检测模式
        echo "## dmidecode -t baseboard输出:" >>"$raw_hw_file"
        dmidecode -t baseboard 2>/dev/null >>"$raw_hw_file" || echo "dmidecode -t baseboard命令执行失败" >>"$raw_hw_file"

        echo -e "\n## dmidecode -t system输出:" >>"$raw_hw_file"
        dmidecode -t system 2>/dev/null >>"$raw_hw_file" || echo "dmidecode -t system命令执行失败" >>"$raw_hw_file"

        echo -e "\n## dmidecode -t bios输出:" >>"$raw_hw_file"
        dmidecode -t bios 2>/dev/null >>"$raw_hw_file" || echo "dmidecode -t bios命令执行失败" >>"$raw_hw_file"

        echo -e "\n# CPU信息" >>"$raw_hw_file"
        echo "## lscpu输出:" >>"$raw_hw_file"
        lscpu 2>/dev/null >>"$raw_hw_file" || echo "lscpu命令执行失败" >>"$raw_hw_file"

        echo -e "\n## /proc/cpuinfo输出:" >>"$raw_hw_file"
        cat /proc/cpuinfo 2>/dev/null >>"$raw_hw_file" || echo "/proc/cpuinfo读取失败" >>"$raw_hw_file"

        echo -e "\n# 内存信息" >>"$raw_hw_file"
        echo "## free -h输出:" >>"$raw_hw_file"
        free -h 2>/dev/null >>"$raw_hw_file" || echo "free命令执行失败" >>"$raw_hw_file"

        echo -e "\n## dmidecode -t memory输出:" >>"$raw_hw_file"
        dmidecode -t memory 2>/dev/null >>"$raw_hw_file" || echo "dmidecode命令执行失败" >>"$raw_hw_file"

        echo -e "\n## /proc/meminfo输出:" >>"$raw_hw_file"
        cat /proc/meminfo 2>/dev/null >>"$raw_hw_file" || echo "/proc/meminfo读取失败" >>"$raw_hw_file"

        echo -e "\n# 存储信息" >>"$raw_hw_file"
        echo "## lsblk输出:" >>"$raw_hw_file"
        lsblk 2>/dev/null >>"$raw_hw_file" || echo "lsblk命令执行失败" >>"$raw_hw_file"

        echo -e "\n## df -h输出:" >>"$raw_hw_file"
        df -h 2>/dev/null >>"$raw_hw_file" || echo "df命令执行失败" >>"$raw_hw_file"

        echo -e "\n## fdisk -l输出:" >>"$raw_hw_file"
        fdisk -l 2>/dev/null >>"$raw_hw_file" || echo "fdisk命令执行失败" >>"$raw_hw_file"
    elif [[ "$node" == "n1" ]]; then
        # AFW3000设备使用show ver命令
        ssh_execute "$host" "$user" "$password" "$port" "show ver" 2>/dev/null | grep -E "(CPU|处理器)" >>"$raw_hw_file" 2>/dev/null || echo "无CPU信息" >>"$raw_hw_file"
    else
        # Linux系统使用标准命令
        echo "## dmidecode -t baseboard输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "dmidecode -t baseboard 2>/dev/null" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "dmidecode -t baseboard命令执行失败" >>"$raw_hw_file"

        echo -e "\n## dmidecode -t system输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "dmidecode -t system 2>/dev/null" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "dmidecode -t system命令执行失败" >>"$raw_hw_file"

        echo -e "\n## dmidecode -t bios输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "dmidecode -t bios 2>/dev/null" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "dmidecode -t bios命令执行失败" >>"$raw_hw_file"

        echo -e "\n# CPU信息" >>"$raw_hw_file"
        echo "## lscpu输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "lscpu" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "lscpu命令执行失败" >>"$raw_hw_file"

        echo -e "\n## /proc/cpuinfo输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "cat /proc/cpuinfo" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "/proc/cpuinfo读取失败" >>"$raw_hw_file"

        echo -e "\n# 内存信息" >>"$raw_hw_file"
        echo "## free -h输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "free -h" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "free命令执行失败" >>"$raw_hw_file"

        echo -e "\n## dmidecode -t memory输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "dmidecode -t memory 2>/dev/null" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "dmidecode命令执行失败" >>"$raw_hw_file"

        echo -e "\n## /proc/meminfo输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "cat /proc/meminfo" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "/proc/meminfo读取失败" >>"$raw_hw_file"

        echo -e "\n# 存储信息" >>"$raw_hw_file"
        echo "## lsblk输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "lsblk" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "lsblk命令执行失败" >>"$raw_hw_file"

        echo -e "\n## df -h输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "df -h" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "df命令执行失败" >>"$raw_hw_file"

        echo -e "\n## fdisk -l输出:" >>"$raw_hw_file"
        ssh_execute "$host" "$user" "$password" "$port" "fdisk -l 2>/dev/null" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_hw_file" || echo "fdisk命令执行失败" >>"$raw_hw_file"
    fi
}

# 收集系统信息相关的原始命令输出
collect_raw_system_data() {
    local node="$1"
    local host="$2"
    local user="$3"
    local password="$4"
    local port="$5"
    local raw_data_dir="$6"

    local raw_sys_file="$raw_data_dir/system_${node}.txt"

    cat >"$raw_sys_file" <<'EOF'
# 系统版本信息
EOF

    if [[ "$node" == "localhost" ]]; then
        # 本地检测模式
        echo "## uname -a输出:" >>"$raw_sys_file"
        uname -a 2>/dev/null >>"$raw_sys_file" || echo "uname命令执行失败" >>"$raw_sys_file"

        echo -e "\n## /etc/os-release输出:" >>"$raw_sys_file"
        cat /etc/os-release 2>/dev/null >>"$raw_sys_file" || echo "/etc/os-release读取失败" >>"$raw_sys_file"

        echo -e "\n## hostnamectl输出:" >>"$raw_sys_file"
        hostnamectl 2>/dev/null >>"$raw_sys_file" || echo "hostnamectl命令执行失败" >>"$raw_sys_file"

        echo -e "\n# 系统状态信息" >>"$raw_sys_file"
        echo "## uptime输出:" >>"$raw_sys_file"
        uptime 2>/dev/null >>"$raw_sys_file" || echo "uptime命令执行失败" >>"$raw_sys_file"

        echo -e "\n## top -bn1输出:" >>"$raw_sys_file"
        top -bn1 | head -20 2>/dev/null >>"$raw_sys_file" || echo "top命令执行失败" >>"$raw_sys_file"

        echo -e "\n## ps aux输出:" >>"$raw_sys_file"
        ps aux 2>/dev/null >>"$raw_sys_file" || echo "ps命令执行失败" >>"$raw_sys_file"
    elif [[ "$node" == "n1" ]]; then
        # AFW3000设备
        echo "## show ver输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "show ver" 2>/dev/null >>"$raw_sys_file" || echo "show ver命令执行失败" >>"$raw_sys_file"
    else
        # Linux系统
        echo "## uname -a输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "uname -a" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "uname命令执行失败" >>"$raw_sys_file"

        echo -e "\n## /etc/os-release输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "cat /etc/os-release" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "/etc/os-release读取失败" >>"$raw_sys_file"

        echo -e "\n## hostnamectl输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "hostnamectl" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "hostnamectl命令执行失败" >>"$raw_sys_file"

        echo -e "\n# 系统状态信息" >>"$raw_sys_file"
        echo "## uptime输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "uptime" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "uptime命令执行失败" >>"$raw_sys_file"

        echo -e "\n## top -bn1输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "top -bn1 | head -20" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "top命令执行失败" >>"$raw_sys_file"

        echo -e "\n## ps aux输出:" >>"$raw_sys_file"
        ssh_execute "$host" "$user" "$password" "$port" "ps aux" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_sys_file" || echo "ps命令执行失败" >>"$raw_sys_file"
    fi
}

# 收集网络相关的原始命令输出
collect_raw_network_data() {
    local node="$1"
    local host="$2"
    local user="$3"
    local password="$4"
    local port="$5"
    local raw_data_dir="$6"

    local raw_net_file="$raw_data_dir/network_${node}.txt"

    cat >"$raw_net_file" <<'EOF'
# 网络接口信息
EOF

    if [[ "$node" == "localhost" ]]; then
        # 本地检测模式
        echo "## ip addr输出:" >>"$raw_net_file"
        ip addr 2>/dev/null >>"$raw_net_file" || echo "ip addr命令执行失败" >>"$raw_net_file"

        echo -e "\n## ifconfig输出:" >>"$raw_net_file"
        ifconfig 2>/dev/null >>"$raw_net_file" || echo "ifconfig命令执行失败" >>"$raw_net_file"

        echo -e "\n## ip route输出:" >>"$raw_net_file"
        ip route 2>/dev/null >>"$raw_net_file" || echo "ip route命令执行失败" >>"$raw_net_file"

        echo -e "\n## netstat -tuln输出:" >>"$raw_net_file"
        netstat -tuln 2>/dev/null >>"$raw_net_file" || echo "netstat命令执行失败" >>"$raw_net_file"

        echo -e "\n## ss -tuln输出:" >>"$raw_net_file"
        ss -tuln 2>/dev/null >>"$raw_net_file" || echo "ss命令执行失败" >>"$raw_net_file"

        echo -e "\n## /etc/resolv.conf输出:" >>"$raw_net_file"
        cat /etc/resolv.conf 2>/dev/null >>"$raw_net_file" || echo "/etc/resolv.conf读取失败" >>"$raw_net_file"

        # 添加ethtool命令收集网口速率信息
        echo -e "\n## ethtool输出:" >>"$raw_net_file"
        # 检查ethtool命令是否可用
        if command -v ethtool >/dev/null 2>&1; then
            # 获取所有物理网口（排除lo、sit0、ip6tnl0等虚拟接口）
            local interfaces=$(ip link show 2>/dev/null | grep -E '^[0-9]+:' | awk -F': ' '{print $2}' | grep -v -E '^(lo|sit[0-9]*|ip6tnl[0-9]*|virbr[0-9]*|docker[0-9]*|veth[0-9]*|tunl[0-9]*|gre[0-9]*|gretap[0-9]*|erspan[0-9]*|ip_vti[0-9]*|ip6_vti[0-9]*|ip6gre[0-9]*|br-[0-9a-f]*|tun[0-9]*|tap[0-9]*)(@.*)?$')
            if [[ -n "$interfaces" ]]; then
                while IFS= read -r interface; do
                    if [[ -n "$interface" ]]; then
                        echo "### $interface:" >>"$raw_net_file"
                        # 处理带@符号的接口名称，ethtool需要使用@符号前的部分
                        local ethtool_interface="${interface%%@*}"
                        local ethtool_output=$(ethtool "$ethtool_interface" 2>/dev/null)
                        if [[ $? -eq 0 && -n "$ethtool_output" ]]; then
                            echo "$ethtool_output" >>"$raw_net_file"
                        else
                            echo "ethtool $ethtool_interface 命令执行失败或无权限" >>"$raw_net_file"
                        fi
                    fi
                done <<<"$interfaces"
            else
                echo "未找到可检测的物理网络接口" >>"$raw_net_file"
            fi
        else
            echo "ethtool命令不可用，请安装ethtool工具" >>"$raw_net_file"
        fi
    elif [[ "$node" == "n1" ]]; then
        # AFW3000设备
        echo "## show interface输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "show interface" 2>/dev/null >>"$raw_net_file" || echo "show interface命令执行失败" >>"$raw_net_file"
    else
        # Linux系统
        echo "## ip addr输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "ip addr" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "ip addr命令执行失败" >>"$raw_net_file"

        echo -e "\n## ifconfig输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "ifconfig" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "ifconfig命令执行失败" >>"$raw_net_file"

        echo -e "\n## ip route输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "ip route" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "ip route命令执行失败" >>"$raw_net_file"

        echo -e "\n## netstat -tuln输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "netstat -tuln" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "netstat命令执行失败" >>"$raw_net_file"

        echo -e "\n## ss -tuln输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "ss -tuln" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "ss命令执行失败" >>"$raw_net_file"

        echo -e "\n## /etc/resolv.conf输出:" >>"$raw_net_file"
        ssh_execute "$host" "$user" "$password" "$port" "cat /etc/resolv.conf" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "/etc/resolv.conf读取失败" >>"$raw_net_file"

        # 添加ethtool命令收集网口速率信息
        echo -e "\n## ethtool输出:" >>"$raw_net_file"
        # 检查远程主机ethtool命令可用性并收集信息
        local remote_ethtool_check=$(ssh_execute "$host" "$user" "$password" "$port" "command -v ethtool >/dev/null 2>&1 && echo 'available' || echo 'not_available'" 2>/dev/null | grep -v "INFO\|ERROR\|WARN")

        if [[ "$remote_ethtool_check" == "available" ]]; then
            # 获取远程主机的网络接口列表并执行ethtool
            local remote_interfaces=$(ssh_execute "$host" "$user" "$password" "$port" "ip link show 2>/dev/null | grep -E '^[0-9]+:' | awk -F': ' '{print \$2}' | grep -v -E '^(lo|sit[0-9]*|ip6tnl[0-9]*|virbr[0-9]*|docker[0-9]*|veth[0-9]*|tunl[0-9]*|gre[0-9]*|gretap[0-9]*|erspan[0-9]*|ip_vti[0-9]*|ip6_vti[0-9]*|ip6gre[0-9]*|br-[0-9a-f]*|tun[0-9]*|tap[0-9]*)(@.*)?$'" 2>/dev/null | grep -v "INFO\|ERROR\|WARN")
            if [[ -n "$remote_interfaces" ]]; then
                while IFS= read -r interface; do
                    if [[ -n "$interface" ]]; then
                        echo "### $interface:" >>"$raw_net_file"
                        # 处理带@符号的接口名称，ethtool需要使用@符号前的部分
                        local ethtool_interface="${interface%%@*}"
                        local ethtool_result=$(ssh_execute "$host" "$user" "$password" "$port" "ethtool $ethtool_interface 2>/dev/null" 2>/dev/null | grep -v "INFO\|ERROR\|WARN")
                        if [[ -n "$ethtool_result" ]]; then
                            echo "$ethtool_result" >>"$raw_net_file"
                        else
                            echo "ethtool $ethtool_interface 命令执行失败或无权限" >>"$raw_net_file"
                        fi
                    fi
                done <<<"$remote_interfaces"
            else
                echo "未找到可检测的物理网络接口" >>"$raw_net_file"
            fi
        else
            echo "ethtool命令不可用，请在远程主机安装ethtool工具" >>"$raw_net_file"
        fi

        echo -e "\n## ping测试结果:" >>"$raw_net_file"
        ping -c 3 "$host" 2>/dev/null | grep -v "INFO\|ERROR\|WARN" >>"$raw_net_file" || echo "ping测试失败" >>"$raw_net_file"
    fi
}

# 收集所有原始数据
collect_all_raw_data() {
    local node="$1"
    local host="$2"
    local user="$3"
    local password="$4"
    local port="$5"
    local raw_data_dir="$6"

    # 确保原始数据目录存在
    if [[ -n "$raw_data_dir" ]]; then
        mkdir -p "$raw_data_dir"
    else
        echo "错误: 原始数据目录参数为空"
        return 1
    fi
    
    # 收集硬件相关的原始命令输出
    collect_raw_hardware_data "$node" "$host" "$user" "$password" "$port" "$raw_data_dir"
    
    # 收集系统信息相关的原始命令输出
    collect_raw_system_data "$node" "$host" "$user" "$password" "$port" "$raw_data_dir"
    
    # 收集网络相关的原始命令输出
    collect_raw_network_data "$node" "$host" "$user" "$password" "$port" "$raw_data_dir"
}

# =============================================================================
# 模块接口函数
# =============================================================================

# 主接口函数
main() {
    local action="$1"
    shift
    
    case "$action" in
        "hardware")
            collect_raw_hardware_data "$@"
            ;;
        "system")
            collect_raw_system_data "$@"
            ;;
        "network")
            collect_raw_network_data "$@"
            ;;
        "all")
            collect_all_raw_data "$@"
            ;;
        *)
            echo "错误: 未知的操作 '$action'"
            echo "用法: $0 <action> [参数...]"
            echo "支持的操作: hardware, system, network, all"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本，调用主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
