#!/bin/bash
# 报告生成模块
# 功能: 统一的报告生成和格式化
# 版本: 1.0

# =============================================================================
# 工具函数
# =============================================================================

# 格式化软件信息函数
format_software_info() {
    local software_name="$1"
    local version_cmd="$2"
    local description="$3"
    local status="$4"  # 运行中/已安装/未安装

    # 获取版本信息
    local version_info=""
    if [[ -n "$version_cmd" && "$version_cmd" != "-" ]]; then
        version_info=$(eval "$version_cmd" 2>/dev/null | head -1)
        if [[ -n "$version_info" ]]; then
            version_info=" $version_info"
        fi
    fi

    # 格式化输出：软件名 版本 (状态)
    echo "${software_name}${version_info} (${status})"
}

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

# =============================================================================
# 模块信息
# =============================================================================

MODULE_NAME="报告生成模块"
MODULE_VERSION="1.0"

# =============================================================================
# 报告生成接口
# =============================================================================

# 生成报告头部
generate_report_header() {
    local detection_mode="$1"  # local/remote
    local target_info="$2"     # 检测范围信息
    local script_version="$3"  # 脚本版本
    
    echo "AFW3000防火墙设备检测报告"
    echo "========================================"
    echo ""
    echo "检测时间：$(date '+%Y-%m-%d %H:%M:%S')"
    
    if [[ "$detection_mode" == "local" ]]; then
        echo "检测范围：本地主机 ($target_info)"
    else
        echo "检测范围：$target_info"
    fi
    
    echo "脚本版本：${script_version:-"1.0"}"
    echo ""
}

# 生成设备概况统计
generate_device_summary() {
    local total_nodes="$1"
    local success_nodes="$2"
    local failed_nodes="$3"
    local success_rate="$4"
    
    echo "========================================"
    echo "设备检测概况"
    echo "========================================"
    echo ""
    echo "总节点数：$total_nodes"
    echo "检测成功：$success_nodes"
    echo "检测失败：$failed_nodes"
    echo "检测成功率：$success_rate"
    echo ""
}

# 生成设备详细检测结果头部
generate_detailed_results_header() {
    echo "========================================"
    echo "设备详细检测结果"
    echo "========================================"
    echo ""
}

# 格式化单个设备的检测结果
format_device_result() {
    local device_name="$1"
    local device_ip="$2"
    local network_status="$3"
    local network_details="$4"
    local hardware_status="$5"
    local hardware_details="$6"
    local system_status="$7"
    local system_details="$8"
    local software_status="$9"
    local software_details="${10}"

    # 设备标识
    if [[ "$device_name" == "localhost" ]]; then
        echo "[$device_ip]"
    else
        echo "[$device_name - $device_ip]"
    fi

    # 网络检测结果
    echo "├── 网络检测：$network_status"
    if [[ -n "$network_details" ]]; then
        echo "$network_details" | sed 's/^/│   /'
    fi

    # 硬件信息结果
    echo "├── 硬件信息：$hardware_status"
    if [[ -n "$hardware_details" ]]; then
        echo "$hardware_details" | sed 's/^/│   /'
    fi

    # 系统信息结果
    echo "├── 系统信息：$system_status"
    if [[ -n "$system_details" ]]; then
        echo "$system_details" | sed 's/^/│   /'
    fi

    # 软件信息结果
    echo "└── 软件信息：$software_status"
    if [[ -n "$software_details" ]]; then
        echo "$software_details" | sed 's/^/    /'
    fi

    echo ""
}

# 格式化本地检测设备结果
format_local_device_result() {
    local hostname="$1"
    local local_ip="$2"
    local network_status="$3"
    local network_details="$4"
    local hardware_status="$5"
    local hardware_details="$6"
    local system_status="$7"
    local system_details="$8"
    local software_status="$9"
    local software_details="${10}"

    # 本地设备标识
    echo "[本地主机 - $hostname ($local_ip)]"

    # 网络检测结果
    echo "├── 网络检测：$network_status"
    if [[ -n "$network_details" ]]; then
        echo "$network_details" | sed 's/^/│   /'
    fi

    # 硬件信息结果
    echo "├── 硬件信息：$hardware_status"
    if [[ -n "$hardware_details" ]]; then
        echo "$hardware_details" | sed 's/^/│   /'
    fi

    # 系统信息结果
    echo "├── 系统信息：$system_status"
    if [[ -n "$system_details" ]]; then
        echo "$system_details" | sed 's/^/│   /'
    fi

    # 软件信息结果
    echo "└── 软件信息：$software_status"
    if [[ -n "$software_details" ]]; then
        echo "$software_details" | sed 's/^/    /'
    fi

    echo ""
}

# 格式化网络检测详细信息
format_network_details() {
    local detection_mode="$1"  # local/remote
    local device_type="$2"     # n1/n2/n3/localhost
    local port_results="$3"    # 端口检测结果

    if [[ "$detection_mode" == "local" ]]; then
        # 本地检测端口格式
        echo "$port_results" | while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                echo "├── $line"
            fi
        done | sed '$s/├──/└──/'
    else
        # 远程检测端口格式
        echo "$port_results" | while IFS= read -r line; do
            if [[ -n "$line" ]]; then
                echo "├── $line"
            fi
        done | sed '$s/├──/└──/'
    fi
}

# 生成原始命令输出附录头部
generate_raw_output_header() {
    echo "================================================================================"
    echo "原始命令输出附录"
    echo "================================================================================"
    echo ""
}

# 格式化原始命令输出
format_raw_output() {
    local node_name="$1"
    local raw_data_file="$2"

    # 特殊处理：如果node_name是"header"，则生成原始数据附录头部
    if [[ "$node_name" == "header" ]]; then
        echo "================================================================================"
        echo "原始命令输出附录"
        echo "================================================================================"
        echo ""
        return 0
    fi

    if [[ "$node_name" == "localhost" ]]; then
        echo "[本地主机节点原始命令输出]"
    else
        local node_upper=$(echo "$node_name" | tr '[:lower:]' '[:upper:]')
        echo "[${node_upper}节点原始命令输出]"
    fi
    echo "========================================"
    echo ""

    if [[ -f "$raw_data_file" ]]; then
        cat "$raw_data_file"
        echo ""
        echo "========================================"
        echo ""
    else
        echo "无原始数据文件"
        echo ""
        echo "========================================"
        echo ""
    fi
}

# 生成报告尾部
generate_report_footer() {
    echo "================================================================================"
    echo "附录结束"
    echo "================================================================================"
    echo ""
    echo "报告生成时间：$(date '+%Y-%m-%d %H:%M:%S')"
}

# =============================================================================
# 主报告生成函数
# =============================================================================

# 解析设备检测数据
parse_device_data() {
    local data_string="$1"
    local device_name="$2"

    # 从数据字符串中提取各部分信息
    # 数据格式: device_name|device_ip|network_status|network_details|hardware_status|hardware_details|system_status|system_details|software_status|software_details
    echo "$data_string" | grep "^$device_name|" | head -1
}

# 生成完整报告
generate_complete_report() {
    local detection_mode="$1"     # local/remote
    local target_info="$2"        # 检测范围信息
    local script_version="$3"     # 脚本版本
    local total_nodes="$4"        # 总节点数
    local success_nodes="$5"      # 成功节点数
    local failed_nodes="$6"       # 失败节点数
    local success_rate="$7"       # 成功率
    local devices_data="$8"       # 设备检测数据（分隔符格式）
    local raw_data_dir="$9"       # 原始数据目录

    # 生成报告头部
    generate_report_header "$detection_mode" "$target_info" "$script_version"

    # 生成设备概况
    generate_device_summary "$total_nodes" "$success_nodes" "$failed_nodes" "$success_rate"

    # 生成详细检测结果头部
    generate_detailed_results_header

    # 处理设备数据
    if [[ -n "$devices_data" ]]; then
        echo "$devices_data" | while IFS='|' read -r device_name device_ip network_status network_details hardware_status hardware_details system_status system_details software_status software_details; do
            if [[ -n "$device_name" ]]; then
                if [[ "$detection_mode" == "local" ]]; then
                    format_local_device_result "$device_name" "$device_ip" "$network_status" "$network_details" "$hardware_status" "$hardware_details" "$system_status" "$system_details" "$software_status" "$software_details"
                else
                    format_device_result "$device_name" "$device_ip" "$network_status" "$network_details" "$hardware_status" "$hardware_details" "$system_status" "$system_details" "$software_status" "$software_details"
                fi
            fi
        done
    fi

    # 生成原始命令输出附录
    generate_raw_output_header

    # 处理原始数据文件
    if [[ -d "$raw_data_dir" ]]; then
        for raw_file in "$raw_data_dir"/*.txt; do
            if [[ -f "$raw_file" ]]; then
                local node_name=$(basename "$raw_file" .txt | sed 's/.*_//')
                format_raw_output "$node_name" "$raw_file"
            fi
        done
    fi

    # 生成报告尾部
    generate_report_footer
}

# 生成简化报告（仅包含检测结果，不包含原始数据）
generate_simple_report() {
    local detection_mode="$1"
    local target_info="$2"
    local script_version="$3"
    local total_nodes="$4"
    local success_nodes="$5"
    local failed_nodes="$6"
    local success_rate="$7"
    local devices_data="$8"

    # 生成报告头部
    generate_report_header "$detection_mode" "$target_info" "$script_version"

    # 生成设备概况
    generate_device_summary "$total_nodes" "$success_nodes" "$failed_nodes" "$success_rate"

    # 生成详细检测结果头部
    generate_detailed_results_header

    # 处理设备数据
    if [[ -n "$devices_data" ]]; then
        echo "$devices_data" | while IFS='|' read -r device_name device_ip network_status network_details hardware_status hardware_details system_status system_details software_status software_details; do
            if [[ -n "$device_name" ]]; then
                if [[ "$detection_mode" == "local" ]]; then
                    format_local_device_result "$device_name" "$device_ip" "$network_status" "$network_details" "$hardware_status" "$hardware_details" "$system_status" "$system_details" "$software_status" "$software_details"
                else
                    format_device_result "$device_name" "$device_ip" "$network_status" "$network_details" "$hardware_status" "$hardware_details" "$system_status" "$system_details" "$software_status" "$software_details"
                fi
            fi
        done
    fi

    # 生成简化的报告尾部
    echo "报告生成时间：$(date '+%Y-%m-%d %H:%M:%S')"
}

# =============================================================================
# 辅助函数
# =============================================================================

# 获取设备配置信息（从配置文件读取）
get_device_config() {
    local node="$1"

    # 配置文件路径
    local config_file="config/devices.conf"

    # 如果配置文件不存在，使用默认配置
    if [[ ! -f "$config_file" ]]; then
        case "$node" in
            "n1")
                echo "************* admin admin123 22 backup backup123"
                ;;
            "n2")
                echo "************* root root123 22 backup backup123"
                ;;
            "n3")
                echo "************* root root123 22 backup backup123"
                ;;
            *)
                echo "Unknown Unknown Unknown Unknown Unknown Unknown"
                ;;
        esac
        return
    fi

    # 从配置文件读取配置信息
    case "$node" in
        "n1")
            local host=$(grep "^N1_HOST=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local user=$(grep "^N1_USER=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local password=$(grep "^N1_PASSWORD=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local port=$(grep "^N1_PORT=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            echo "${host:-*************} ${user:-admin} ${password:-admin123} ${port:-22} backup backup123"
            ;;
        "n2")
            local host=$(grep "^N2_HOST=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local user=$(grep "^N2_USER=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local password=$(grep "^N2_PASSWORD=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local port=$(grep "^N2_PORT=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local backup_user=$(grep "^N2_BACKUP_USER=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local backup_password=$(grep "^N2_BACKUP_PASSWORD=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            echo "${host:-*************} ${user:-root} ${password:-root123} ${port:-22} ${backup_user:-backup} ${backup_password:-backup123}"
            ;;
        "n3")
            local host=$(grep "^N3_HOST=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local user=$(grep "^N3_USER=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local password=$(grep "^N3_PASSWORD=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local port=$(grep "^N3_PORT=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local backup_user=$(grep "^N3_BACKUP_USER=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            local backup_password=$(grep "^N3_BACKUP_PASSWORD=" "$config_file" | cut -d'=' -f2 | tr -d '"')
            echo "${host:-*************} ${user:-root} ${password:-root123} ${port:-22} ${backup_user:-backup} ${backup_password:-backup123}"
            ;;
        *)
            echo "Unknown Unknown Unknown Unknown Unknown Unknown"
            ;;
    esac
}

# =============================================================================
# 数据提取和处理函数
# =============================================================================

# 提取硬件检测详细信息
extract_hardware_details() {
    local temp_hardware_file="$1"
    local hardware_details=""

    if [[ ! -f "$temp_hardware_file" ]]; then
        echo "│   └── 硬件检测文件不存在"
        return 1
    fi

    # 提取硬件检测的详细输出
    local hw_output=$(sed -n '/HARDWARE_CHECK_OUTPUT_START/,/HARDWARE_CHECK_OUTPUT_END/p' "$temp_hardware_file" | sed '1d;$d')

    # 提取主板信息（如果检测失败则显示Unknown）
    local mb_manufacturer="Unknown"
    local mb_model="Unknown"
    local mb_bios="Unknown"

    if echo "$hw_output" | grep -q "主板信息获取成功"; then
        mb_manufacturer=$(echo "$hw_output" | grep "制造商:" | cut -d':' -f2 | xargs)
        mb_model=$(echo "$hw_output" | grep "型号:" | head -1 | cut -d':' -f2 | xargs)
        mb_bios=$(echo "$hw_output" | grep "版本:" | cut -d':' -f2 | xargs)
    fi

    # 提取CPU信息
    local cpu_model="Unknown"
    local cpu_cores="Unknown"
    local cpu_freq="Unknown"

    if echo "$hw_output" | grep -q "CPU信息获取成功"; then
        # 提取CPU信息块，避免与主板信息混淆
        local cpu_section=$(echo "$hw_output" | sed -n '/CPU信息获取成功/,/^$/p')
        cpu_model=$(echo "$cpu_section" | grep "型号:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        cpu_cores=$(echo "$cpu_section" | grep "核数:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        cpu_freq=$(echo "$cpu_section" | grep "频率:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    fi

    # 提取内存信息
    local mem_total="Unknown"
    if echo "$hw_output" | grep -q "内存信息获取成功"; then
        # 修复：使用sed替代cut处理中文冒号，避免"分界符必须是单个字符"错误
        mem_total=$(echo "$hw_output" | grep "总容量：" | head -1 | sed 's/.*总容量：//' | xargs)
    fi

    # 提取存储信息
    local storage_total="Unknown"
    local storage_details=""
    if echo "$hw_output" | grep -q "存储信息获取成功"; then
        # 修复：使用sed替代cut处理中文冒号，避免"分界符必须是单个字符"错误
        storage_total=$(echo "$hw_output" | grep "总容量：" | tail -1 | sed 's/.*总容量：//' | xargs)

        # 提取硬盘列表
        local in_disk_list=false
        local disk_count=0
        while IFS= read -r line; do
            if [[ "$line" =~ "硬盘列表:" ]]; then
                in_disk_list=true
                continue
            fi
            if [[ "$in_disk_list" == true ]]; then
                if [[ "$line" =~ ^[[:space:]]*[├└]──.*硬盘[0-9] ]]; then
                    local disk_info=$(echo "$line" | sed 's/^[[:space:]]*[├└]──[[:space:]]*//')
                    # 过滤掉包含 === 的异常条目
                    if [[ "$disk_info" =~ === ]]; then
                        continue
                    fi
                    disk_count=$((disk_count + 1))
                    if [[ $disk_count -eq 1 ]]; then
                        storage_details="│   │       ├── $disk_info"$'\n'
                    else
                        storage_details+="│   │       ├── $disk_info"$'\n'
                    fi
                elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                    break
                fi
            fi
        done <<<"$hw_output"

        # 修改最后一个硬盘的符号
        if [[ -n "$storage_details" ]]; then
            storage_details=$(echo "$storage_details" | sed '$s/├──/└──/')
        else
            storage_details="│   │       └── 硬盘1: 型号=Unknown, 容量=${storage_total}, 类型=Unknown"
        fi
    else
        storage_details="│   │       └── 硬盘1: 型号=Unknown, 容量=${storage_total}, 类型=Unknown"
    fi

    # 提取网口信息
    local nic_total="Unknown"
    local nic_details="│           └── eth0: IP=Unknown, MAC=Unknown, 速率=Unknown"

    if echo "$hw_output" | grep -q "网口信息获取成功"; then
        nic_total=$(echo "$hw_output" | grep "网口总数:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

        # 提取网口列表
        local nic_list=""
        local in_nic_list=false
        while IFS= read -r line; do
            if [[ "$line" =~ "网口列表:" ]]; then
                in_nic_list=true
                continue
            fi
            if [[ "$in_nic_list" == true ]]; then
                if [[ "$line" =~ ^[[:space:]]*[├└]── ]]; then
                    local nic_info=$(echo "$line" | sed 's/^[[:space:]]*[├└]──[[:space:]]*//')
                    nic_list+="│           ├── $nic_info"$'\n'
                elif [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" =~ "========" ]]; then
                    break
                fi
            fi
        done <<<"$hw_output"

        if [[ -n "$nic_list" ]]; then
            nic_details=$(echo "$nic_list" | sed '$s/├──/└──/')
        fi
    fi

    # 构建硬件详细信息
    hardware_details="│   ├── 主板："$'\n'
    hardware_details+="│   │   ├── 制造商：${mb_manufacturer}"$'\n'
    hardware_details+="│   │   ├── 型号：${mb_model}"$'\n'
    hardware_details+="│   │   └── 版本：${mb_bios}"$'\n'
    hardware_details+="│   ├── CPU："$'\n'
    hardware_details+="│   │   ├── 型号：${cpu_model}"$'\n'
    hardware_details+="│   │   ├── 核数：${cpu_cores}"$'\n'
    hardware_details+="│   │   └── 频率：${cpu_freq}"$'\n'
    # 提取内存条详细信息
    local mem_details=""
    if echo "$hw_output" | grep -q "内存信息获取成功"; then
        local mem_section=$(echo "$hw_output" | sed -n '/内存条列表:/,/^$/p')
        local mem_count=0
        while IFS= read -r line; do
            if [[ "$line" =~ ^[[:space:]]*└──[[:space:]]*内存条[0-9]+: ]]; then
                local mem_info=$(echo "$line" | sed 's/^[[:space:]]*└──[[:space:]]*//')
                mem_details+="│   │       └── $mem_info"$'\n'
                mem_count=$((mem_count + 1))
            fi
        done <<<"$mem_section"

        if [[ $mem_count -eq 0 ]]; then
            mem_details="│   │       └── 内存条1: 型号=-, 容量=${mem_total}, 类型=-"$'\n'
        fi
    else
        mem_details="│   │       └── 内存条1: 型号=Unknown, 容量=${mem_total}, 类型=Unknown"$'\n'
    fi

    hardware_details+="│   ├── 内存："$'\n'
    hardware_details+="│   │   ├── 总容量：${mem_total}"$'\n'
    hardware_details+="│   │   └── 内存条列表："$'\n'
    hardware_details+="${mem_details}"
    hardware_details+="│   ├── 存储："$'\n'
    hardware_details+="│   │   ├── 总容量：${storage_total}"$'\n'
    hardware_details+="│   │   └── 硬盘列表："$'\n'
    hardware_details+="${storage_details}"$'\n'
    hardware_details+="│   └── 网口："$'\n'
    hardware_details+="│       ├── 网口总数：${nic_total}"$'\n'
    hardware_details+="│       └── 网口列表："$'\n'
    hardware_details+="${nic_details}"

    echo "$hardware_details"
}

# 提取系统检测详细信息
extract_system_details() {
    local temp_system_file="$1"
    local system_details=""

    if [[ ! -f "$temp_system_file" ]]; then
        echo "│   └── 系统检测文件不存在"
        return 1
    fi

    local sys_exit_code=$(grep "SYSTEM_CHECK_EXIT_CODE=" "$temp_system_file" | cut -d'=' -f2)
    if [[ "$sys_exit_code" -ne 0 ]]; then
        echo "│   └── 系统检测失败"
        return 1
    fi

    # 提取系统检测的详细输出
    local sys_output=$(sed -n '/SYSTEM_CHECK_OUTPUT_START/,/SYSTEM_CHECK_OUTPUT_END/p' "$temp_system_file" | sed '1d;$d')

    # 提取系统版本信息
    local hostname_name=$(echo "$sys_output" | grep "主机名称:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local os_name=$(echo "$sys_output" | grep "发行版本:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local kernel_version=$(echo "$sys_output" | grep "内核版本:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local architecture=$(echo "$sys_output" | grep "系统架构:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

    # 提取系统状态信息
    local load_avg=$(echo "$sys_output" | grep "系统负载:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local mem_usage=$(echo "$sys_output" | grep "内存使用率:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local disk_usage=$(echo "$sys_output" | grep "硬盘使用率:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local uptime=$(echo "$sys_output" | grep "运行时间:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

    # 构建系统详细信息
    system_details="│   ├── 系统版本："$'\n'
    system_details+="│   │   ├── 发行版本：${os_name:-"Unknown"}"$'\n'
    system_details+="│   │   ├── 内核版本：${kernel_version:-"Unknown"}"$'\n'
    system_details+="│   │   └── 系统架构：${architecture:-"Unknown"}"$'\n'
    system_details+="│   └── 系统状态："$'\n'
    system_details+="│       ├── 系统负载：${load_avg:-"-"}"$'\n'
    system_details+="│       ├── 内存使用率：${mem_usage:-"-"}"$'\n'
    system_details+="│       ├── 硬盘使用率：${disk_usage:-"-"}"$'\n'
    system_details+="│       └── 运行时间：${uptime:-"-"}"

    echo "$system_details"
}

# 提取软件检测详细信息
extract_software_details() {
    local temp_software_file="$1"
    local software_details=""

    if [[ ! -f "$temp_software_file" ]]; then
        echo "    └── 软件检测文件不存在"
        return 1
    fi

    # 提取软件检测的详细输出
    local soft_output=$(sed -n '/SOFTWARE_CHECK_OUTPUT_START/,/SOFTWARE_CHECK_OUTPUT_END/p' "$temp_software_file" | sed '1d;$d')

    # 提取服务统计信息
    local service_count=$(echo "$soft_output" | grep "检测服务:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local running_count=$(echo "$soft_output" | grep "安装成功:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
    local not_installed_count=$(echo "$soft_output" | grep "安装失败:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

    # 解析各类别的服务
    local security_suite_success=()
    local security_suite_failed=()
    local security_hardening_success=()
    local security_hardening_failed=()
    local other_software_success=()
    local other_software_failed=()

    # 解析安全加固状态 - 修复：检查实际执行状态而不仅仅是关键字
    local hardening_status="未执行"
    if echo "$soft_output" | grep -A 5 "security_hardening" | grep -q "no_records"; then
        hardening_status="未执行"
    elif echo "$soft_output" | grep -A 5 "security_hardening" | grep -q "状态: 未安装"; then
        hardening_status="未执行"
    elif echo "$soft_output" | grep -A 5 "security_hardening" | grep -q "状态: 运行中"; then
        hardening_status="已执行加固"
    elif echo "$soft_output" | grep -q "security_hardening"; then
        # 如果找到security_hardening但没有明确状态，检查是否有执行记录
        if echo "$soft_output" | grep -A 10 "security_hardening" | grep -q "has_records"; then
            hardening_status="已执行加固"
        else
            hardening_status="未执行"
        fi
    else
        hardening_status="未执行"
    fi

    # 解析软件检测输出，按类别分组
    local current_service=""
    local current_service_name=""
    local current_service_category=""
    local in_service_block=false

    while IFS= read -r line; do
        # 解析格式：正在检测: Watchdog (security_suite)
        if [[ "$line" == *"正在检测:"* ]]; then
            # 提取服务名和类别
            local service_line=$(echo "$line" | cut -d':' -f2- | sed 's/^[[:space:]]*//')
            if [[ "$service_line" == *"("*")"* ]]; then
                current_service=$(echo "$service_line" | cut -d'(' -f1 | sed 's/[[:space:]]*$//')
                current_service_category=$(echo "$service_line" | grep -o '([^)]*)' | sed 's/[()]//g')
                in_service_block=true
            fi
        elif [[ "$line" == *"描述:"* ]] && [[ "$in_service_block" == true ]]; then
            current_service_name=$(echo "$line" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        elif [[ "$line" == *"状态:"* ]] && [[ -n "$current_service" ]] && [[ "$in_service_block" == true ]]; then
            local status=$(echo "$line" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            # 获取版本信息（如果有的话）
            local version_info=""
            local next_line=""

            if [[ "$status" == *"运行中"* ]] || [[ "$status" == *"正常"* ]] || [[ "$status" == *"已安装"* ]]; then
                # 尝试获取版本信息
                local formatted_name=$(format_software_info "$current_service" "" "" "运行中")
                case "$current_service_category" in
                "security_suite")
                    security_suite_success+=("$formatted_name")
                    ;;
                "security_hardening")
                    security_hardening_success+=("$formatted_name")
                    ;;
                "other_software")
                    other_software_success+=("$formatted_name")
                    ;;
                esac
            else
                local formatted_name=$(format_software_info "$current_service" "" "" "未安装")
                case "$current_service_category" in
                "security_suite")
                    security_suite_failed+=("$formatted_name")
                    ;;
                "security_hardening")
                    security_hardening_failed+=("$formatted_name")
                    ;;
                "other_software")
                    other_software_failed+=("$formatted_name")
                    ;;
                esac
            fi
            current_service=""
            current_service_name=""
            current_service_category=""
            in_service_block=false
        fi
    done <<<"$soft_output"

    # 构建软件详细信息
    software_details="    ├── 服务统计："$'\n'
    software_details+="    │   ├── 检测服务：${service_count:-"-"}"$'\n'
    software_details+="    │   ├── 安装成功：${running_count:-"-"}"$'\n'
    software_details+="    │   └── 安装失败：${not_installed_count:-"-"}"$'\n'
    software_details+="    ├── 安全套件："$'\n'
    software_details+="    │   ├── 安装成功："$'\n'
    if [[ ${#security_suite_success[@]} -gt 0 ]]; then
        for i in "${!security_suite_success[@]}"; do
            if [[ $i -eq $((${#security_suite_success[@]} - 1)) ]]; then
                software_details+="    │   │   └── ${security_suite_success[i]}"$'\n'
            else
                software_details+="    │   │   ├── ${security_suite_success[i]}"$'\n'
            fi
        done
    else
        software_details+="    │   │   └── 无"$'\n'
    fi
    software_details+="    │   └── 安装失败："$'\n'
    if [[ ${#security_suite_failed[@]} -gt 0 ]]; then
        for i in "${!security_suite_failed[@]}"; do
            if [[ $i -eq $((${#security_suite_failed[@]} - 1)) ]]; then
                software_details+="    │       └── ${security_suite_failed[i]}"$'\n'
            else
                software_details+="    │       ├── ${security_suite_failed[i]}"$'\n'
            fi
        done
    else
        software_details+="    │       └── 无"$'\n'
    fi
    software_details+="    ├── 安全加固：${hardening_status}"$'\n'
    software_details+="    └── 其他软件："$'\n'
    software_details+="        ├── 安装成功："$'\n'
    if [[ ${#other_software_success[@]} -gt 0 ]]; then
        for i in "${!other_software_success[@]}"; do
            if [[ $i -eq $((${#other_software_success[@]} - 1)) ]]; then
                software_details+="        │   └── ${other_software_success[i]}"$'\n'
            else
                software_details+="        │   ├── ${other_software_success[i]}"$'\n'
            fi
        done
    else
        software_details+="        │   └── 无"$'\n'
    fi
    software_details+="        └── 安装失败："$'\n'
    if [[ ${#other_software_failed[@]} -gt 0 ]]; then
        for i in "${!other_software_failed[@]}"; do
            if [[ $i -eq $((${#other_software_failed[@]} - 1)) ]]; then
                software_details+="            └── ${other_software_failed[i]}"
            else
                software_details+="            ├── ${other_software_failed[i]}"$'\n'
            fi
        done
    else
        software_details+="            └── 无"
    fi

    echo "$software_details"
}

# 处理本地原始数据
process_local_raw_data() {
    local raw_data_dir="$1"

    if [[ ! -d "$raw_data_dir" ]]; then
        echo "[本地主机节点原始命令输出]"
        echo "========================================"
        echo ""
        echo "无原始数据目录"
        echo ""
        echo "========================================"
        echo ""
        return 1
    fi

    # 合并所有原始数据到一个临时文件
    local combined_raw_file="/tmp/combined_raw_localhost_$$"

    {
        echo "# CPU信息"
        echo "## lscpu输出:"
        cat "$raw_data_dir/hardware_localhost.txt" 2>/dev/null | sed -n '/## lscpu输出:/,/^$/p' | tail -n +2
        echo ""
        echo "# 内存信息"
        echo "## free -h输出:"
        cat "$raw_data_dir/hardware_localhost.txt" 2>/dev/null | sed -n '/## free -h输出:/,/^$/p' | tail -n +2
        echo ""
        echo "## dmidecode -t memory输出:"
        cat "$raw_data_dir/hardware_localhost.txt" 2>/dev/null | sed -n '/## dmidecode -t memory输出:/,/^$/p' | tail -n +2
        echo ""
        echo "# 存储信息"
        echo "## lsblk输出:"
        cat "$raw_data_dir/hardware_localhost.txt" 2>/dev/null | sed -n '/## lsblk输出:/,/^$/p' | tail -n +2
        echo ""
        echo "## df -h输出:"
        cat "$raw_data_dir/hardware_localhost.txt" 2>/dev/null | sed -n '/## df -h输出:/,/^$/p' | tail -n +2
        echo ""
        echo "系统信息原始数据"
        echo "--------------"
        cat "$raw_data_dir/system_localhost.txt" 2>/dev/null || echo "无系统信息数据"
        echo ""
        echo "网络配置原始数据"
        echo "--------------"
        cat "$raw_data_dir/network_localhost.txt" 2>/dev/null || echo "无网络配置数据"
    } > "$combined_raw_file"

    # 格式化原始数据输出
    format_raw_output "localhost" "$combined_raw_file"

    # 清理临时文件
    rm -f "$combined_raw_file"
}

# 提取远程硬件检测详细信息
extract_remote_hardware_details() {
    local node="$1"
    local hw_output="$2"

    if [[ "$node" == "n1" ]]; then
        # AFW3000防火墙设备的硬件信息格式
        local mb_platform=$(echo "$hw_output" | grep "平台:" | cut -d':' -f2 | xargs)
        local mb_model=$(echo "$hw_output" | grep "型号:" | head -1 | cut -d':' -f2 | xargs)
        local mb_product=$(echo "$hw_output" | grep "产品:" | cut -d':' -f2 | xargs)

        local cpu_model=$(echo "$hw_output" | grep "型号:" | sed -n '2p' | cut -d':' -f2 | xargs)
        local cpu_cores=$(echo "$hw_output" | grep "核数:" | cut -d':' -f2 | xargs)
        local cpu_freq=$(echo "$hw_output" | grep "频率:" | cut -d':' -f2 | xargs)

        # 修复：使用sed替代cut处理中文冒号，避免"分界符必须是单个字符"错误
        local mem_total=$(echo "$hw_output" | grep "总容量：" | head -1 | sed 's/.*总容量：//' | xargs)
        local storage_total=$(echo "$hw_output" | grep "总容量：" | tail -1 | sed 's/.*总容量：//' | xargs)
        local nic_total=$(echo "$hw_output" | grep "网口总数:" | cut -d':' -f2 | xargs)

        echo "│   ├── 主板："
        echo "│   │   ├── 平台：${mb_platform:-"Unknown"}"
        echo "│   │   ├── 型号：${mb_model:-"Unknown"}"
        echo "│   │   └── 产品：${mb_product:-"Unknown"}"
        echo "│   ├── CPU："
        echo "│   │   ├── 型号：${cpu_model:-"Unknown"}"
        echo "│   │   ├── 核数：${cpu_cores:-"Unknown"}"
        echo "│   │   └── 频率：${cpu_freq:-"Unknown"}"
        echo "│   ├── 内存："
        echo "│   │   ├── 总容量：${mem_total:-"Unknown"}"
        echo "│   │   └── 内存条列表："
        echo "│   │       └── 内存条1: 型号=Unknown, 容量=${mem_total:-"Unknown"}, 类型=Unknown"
        echo "│   ├── 存储："
        echo "│   │   ├── 总容量：${storage_total:-"Unknown"}"
        echo "│   │   └── 硬盘列表："
        echo "│   │       └── 硬盘1: 型号=Unknown, 容量=${storage_total:-"Unknown"}, 类型=Unknown"
        echo "│   └── 网口："

        # 从show interface输出中解析网口信息
        local interface_lines=$(echo "$hw_output" | grep -E "^ ge[0-9]" | grep -v "INFO\|ERROR\|WARN")
        local interface_count=$(echo "$interface_lines" | grep -c "ge[0-9]" 2>/dev/null || echo "0")

        if [[ $interface_count -gt 0 ]]; then
            echo "│       ├── 网口总数：${interface_count}个千兆网口"
            echo "│       └── 网口列表："

            # 解析每个网口的详细信息
            local interface_details=""
            local count=0
            while IFS= read -r line; do
                if [[ -n "$line" && "$line" =~ ge[0-9] ]]; then
                    # 提取网口信息：接口名、IP地址、MAC地址、链路状态
                    local interface_name=$(echo "$line" | awk '{print $1}')
                    local ip_address=$(echo "$line" | awk '{print $2}')
                    local admin_state=$(echo "$line" | awk '{print $3}')
                    local link_state=$(echo "$line" | awk '{print $4}')
                    local mac_address=$(echo "$line" | awk '{print $5}')

                    # 处理IP地址显示
                    if [[ "$ip_address" == "-" ]]; then
                        ip_address="未配置"
                    fi

                    # 处理链路状态
                    local status="Unknown"
                    if [[ "$admin_state" == "U" && "$link_state" == "U" ]]; then
                        status="连接"
                    elif [[ "$admin_state" == "U" && "$link_state" == "D" ]]; then
                        status="断开"
                    else
                        status="禁用"
                    fi

                    count=$((count + 1))
                    if [[ $count -eq $interface_count ]]; then
                        interface_details+="│           └── ${interface_name}: IP=${ip_address}, MAC=${mac_address}, 状态=${status}"
                    else
                        interface_details+="│           ├── ${interface_name}: IP=${ip_address}, MAC=${mac_address}, 状态=${status}"$'\n'
                    fi
                fi
            done <<<"$interface_lines"

            if [[ -n "$interface_details" ]]; then
                echo "$interface_details"
            else
                echo "│           └── 无网口信息"
            fi
        else
            echo "│       ├── 网口总数：${nic_total:-"6个千兆网口"}"
            echo "│       └── 网口列表："
            echo "│           └── ge0: IP=Unknown, MAC=Unknown, 速率=Unknown"
        fi
    else
        # Linux系统的硬件信息格式 - 从原始数据中提取
        # 提取主板信息
        local mb_manufacturer="Unknown"
        local mb_product="Unknown"
        local mb_version="Unknown"

        # 从dmidecode输出中提取主板信息
        if echo "$hw_output" | grep -q "Base Board Information"; then
            mb_manufacturer=$(echo "$hw_output" | grep -A 10 "Base Board Information" | grep "Manufacturer:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            mb_product=$(echo "$hw_output" | grep -A 10 "Base Board Information" | grep "Product Name:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            mb_version=$(echo "$hw_output" | grep -A 10 "Base Board Information" | grep "Version:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        fi

        # 提取CPU信息
        local cpu_model="Unknown"
        local cpu_cores="Unknown"
        local cpu_freq="Unknown"

        # 从lscpu输出中提取CPU信息
        if echo "$hw_output" | grep -q "Model name:"; then
            cpu_model=$(echo "$hw_output" | grep "Model name:" | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        fi
        if echo "$hw_output" | grep -q "CPU(s):"; then
            cpu_cores=$(echo "$hw_output" | grep "CPU(s):" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
        fi
        if echo "$hw_output" | grep -q "CPU MHz:"; then
            cpu_freq=$(echo "$hw_output" | grep "CPU MHz:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            cpu_freq="${cpu_freq}MHz"
        elif echo "$hw_output" | grep -q "CPU max MHz:"; then
            cpu_freq=$(echo "$hw_output" | grep "CPU max MHz:" | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            cpu_freq="${cpu_freq}MHz"
        fi

        # 提取内存信息
        local mem_total="Unknown"
        local mem_details=""
        if echo "$hw_output" | grep -q "Mem:"; then
            mem_total=$(echo "$hw_output" | grep "Mem:" | awk '{print $2}')
        fi

        # 从dmidecode输出中提取内存条详细信息
        if echo "$hw_output" | grep -q "Memory Device"; then
            local mem_count=0
            local mem_info=""
            while IFS= read -r line; do
                if [[ "$line" =~ Memory\ Device ]]; then
                    mem_count=$((mem_count + 1))
                    local size="Unknown"
                    local manufacturer="Unknown"
                    local part_number="Unknown"
                    local speed="Unknown"
                    local locator="Unknown"

                    # 读取接下来的行来获取内存条详细信息
                    local next_lines=""
                    local line_count=0
                    while IFS= read -r next_line && [[ $line_count -lt 25 ]]; do
                        next_lines+="$next_line"$'\n'
                        line_count=$((line_count + 1))
                        if [[ "$next_line" =~ ^Handle\ 0x ]]; then
                            break
                        fi
                    done

                    # 提取内存条信息
                    if echo "$next_lines" | grep -q "Size:"; then
                        size=$(echo "$next_lines" | grep "Size:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                    fi
                    if echo "$next_lines" | grep -q "Manufacturer:"; then
                        manufacturer=$(echo "$next_lines" | grep "Manufacturer:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                    fi
                    if echo "$next_lines" | grep -q "Part Number:"; then
                        part_number=$(echo "$next_lines" | grep "Part Number:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                    fi
                    if echo "$next_lines" | grep -q "Speed:"; then
                        speed=$(echo "$next_lines" | grep "Speed:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                    fi
                    if echo "$next_lines" | grep -q "Locator:"; then
                        locator=$(echo "$next_lines" | grep "Locator:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                    fi

                    # 只处理有效的内存条（Size不为"No Module Installed"）
                    if [[ "$size" != "No Module Installed" && "$size" != "Unknown" ]]; then
                        if [[ $mem_count -eq 1 ]]; then
                            mem_details="│   │       ├── 内存条${mem_count}: 位置=${locator}, 容量=${size}, 制造商=${manufacturer}, 型号=${part_number}, 速率=${speed}"
                        else
                            mem_details+="\n│   │       ├── 内存条${mem_count}: 位置=${locator}, 容量=${size}, 制造商=${manufacturer}, 型号=${part_number}, 速率=${speed}"
                        fi
                    fi
                fi
            done <<<"$hw_output"

            if [[ -n "$mem_details" ]]; then
                # 修改最后一个内存条的符号
                mem_details=$(echo -e "$mem_details" | sed '$s/├──/└──/')
            fi
        fi

        # 如果没有提取到内存条详细信息，使用默认格式
        if [[ -z "$mem_details" ]]; then
            mem_details="│   │       └── 内存条1: 型号=Unknown, 容量=${mem_total}, 类型=Unknown"
        fi

        # 提取存储信息
        local storage_info="Unknown"
        if echo "$hw_output" | grep -q "Disk /dev/"; then
            local disk_count=0
            local storage_details=""
            while IFS= read -r line; do
                if [[ "$line" =~ Disk\ /dev/([^:]+):[[:space:]]*([^,]+) ]]; then
                    local disk_name="${BASH_REMATCH[1]}"
                    local disk_size="${BASH_REMATCH[2]}"

                    # 过滤掉虚拟设备和不需要的设备
                    # 只保留物理硬盘：sda, sdb, sdc, hda, hdb, nvme, mmcblk等
                    # 修复：同时排除启动分区（包含boot关键字的设备）
                    if [[ "$disk_name" =~ ^(sd[a-z]|hd[a-z]|nvme[0-9]+n[0-9]+|mmcblk[0-9]+)$ ]] && [[ ! "$disk_name" =~ boot ]]; then
                        disk_count=$((disk_count + 1))
                        if [[ $disk_count -eq 1 ]]; then
                            storage_details="│   │       ├── 硬盘${disk_count}: 设备=/dev/${disk_name}, 容量=${disk_size}, 类型=Unknown"
                        else
                            storage_details+="\n│   │       ├── 硬盘${disk_count}: 设备=/dev/${disk_name}, 容量=${disk_size}, 类型=Unknown"
                        fi
                    fi
                fi
            done <<<"$hw_output"

            if [[ -n "$storage_details" ]]; then
                # 修改最后一个硬盘的符号
                storage_details=$(echo -e "$storage_details" | sed '$s/├──/└──/')
                storage_info="$storage_details"
            fi
        fi

        # 如果没有提取到存储详细信息，使用默认格式
        if [[ "$storage_info" == "Unknown" ]]; then
            storage_info="│   │       └── 硬盘1: 设备=Unknown, 容量=Unknown, 类型=Unknown"
        fi

        echo "│   ├── 主板："
        echo "│   │   ├── 制造商：${mb_manufacturer}"
        echo "│   │   ├── 型号：${mb_product}"
        echo "│   │   └── 版本：${mb_version}"
        echo "│   ├── CPU："
        echo "│   │   ├── 型号：${cpu_model}"
        echo "│   │   ├── 核数：${cpu_cores}"
        echo "│   │   └── 频率：${cpu_freq}"
        echo "│   ├── 内存："
        echo "│   │   ├── 总容量：${mem_total}"
        echo "│   │   └── 内存条列表："
        echo "$mem_details"
        echo "│   ├── 存储："
        echo "│   │   ├── 总容量：Unknown"
        echo "│   │   └── 硬盘列表："
        echo "$storage_info"
        # 提取网口信息
        local nic_count=0
        local nic_details=""

        # 从ip addr输出中提取网络接口信息
        if echo "$hw_output" | grep -q "## ip addr输出:"; then
            local ip_addr_section=$(echo "$hw_output" | sed -n '/## ip addr输出:/,/^## /p' | sed '$d')

            while IFS= read -r line; do
                # 匹配网络接口行：数字: 接口名: <状态> mtu 大小 ...
                if [[ "$line" =~ ^[0-9]+:[[:space:]]*([^:]+):[[:space:]]*\<([^>]*)\> ]]; then
                    local nic_name="${BASH_REMATCH[1]}"
                    local nic_status="${BASH_REMATCH[2]}"

                    # 过滤掉虚拟接口和回环接口
                    # 只保留物理网络接口：eth, LAN, WAN, ens, enp等
                    if [[ "$nic_name" =~ ^(eth[0-9]+|LAN[0-9]*|WAN[0-9]*|ens[0-9]+|enp[0-9]+s[0-9]+|em[0-9]+)$ ]]; then
                        local nic_ip="Unknown"
                        local nic_mac="Unknown"
                        local nic_speed="Unknown"

                        # 读取接下来的行来获取IP和MAC地址
                        local next_lines=""
                        local line_count=0
                        while IFS= read -r next_line && [[ $line_count -lt 10 ]]; do
                            next_lines+="$next_line"$'\n'
                            line_count=$((line_count + 1))
                            # 如果遇到下一个接口，停止读取
                            if [[ "$next_line" =~ ^[0-9]+:[[:space:]]*[^:]+: ]]; then
                                break
                            fi
                        done

                        # 提取MAC地址
                        if echo "$next_lines" | grep -q "link/ether"; then
                            nic_mac=$(echo "$next_lines" | grep "link/ether" | awk '{print $2}')
                        fi

                        # 提取IP地址（取第一个非回环IP）
                        if echo "$next_lines" | grep -q "inet "; then
                            nic_ip=$(echo "$next_lines" | grep "inet " | grep -v "127.0.0.1" | head -1 | awk '{print $2}' | cut -d'/' -f1)
                        fi

                        # 判断连接状态和速率
                        if [[ "$nic_status" =~ UP ]]; then
                            nic_speed="Connected"
                        else
                            nic_speed="Disconnected"
                        fi

                        nic_count=$((nic_count + 1))
                        if [[ $nic_count -eq 1 ]]; then
                            nic_details="│           ├── ${nic_name}: IP=${nic_ip}, MAC=${nic_mac}, 状态=${nic_speed}"
                        else
                            nic_details+="\n│           ├── ${nic_name}: IP=${nic_ip}, MAC=${nic_mac}, 状态=${nic_speed}"
                        fi
                    fi
                fi
            done <<<"$ip_addr_section"

            if [[ -n "$nic_details" ]]; then
                # 修改最后一个网口的符号
                nic_details=$(echo -e "$nic_details" | sed '$s/├──/└──/')
            fi
        fi

        # 如果没有提取到网口信息，使用默认格式
        if [[ -z "$nic_details" ]]; then
            nic_details="│           └── eth0: IP=Unknown, MAC=Unknown, 状态=Unknown"
            nic_count=1
        fi

        echo "│   └── 网口："
        echo "│       ├── 网口总数：${nic_count}"
        echo "│       └── 网口列表："
        echo "$nic_details"
    fi
}

# 提取远程系统检测详细信息
extract_remote_system_details() {
    local node="$1"
    local sys_output="$2"

    if [[ "$node" == "n1" ]]; then
        # AFW3000防火墙设备的系统信息格式 - 从原始show ver输出中提取
        # 提取防火墙版本信息（包含构建时间）
        local fw_version_line=$(echo "$sys_output" | grep -E "^V[0-9]+\.[0-9]+" | head -1)
        local fw_version=""
        if [[ -n "$fw_version_line" ]]; then
            local version_num=$(echo "$fw_version_line" | sed 's/,.*$//')
            local build_time=$(echo "$fw_version_line" | sed 's/.*Build time is //' | sed 's/$//')
            fw_version="${version_num} (Build ${build_time})"
        fi

        # 提取固件版本
        local firmware_version=$(echo "$sys_output" | grep "Firmware is" | sed 's/.*Firmware is //' | xargs)

        # 提取平台信息
        local platform=$(echo "$sys_output" | grep "Platform" | sed 's/.*Platform[[:space:]]*:[[:space:]]*//' | xargs)

        # 提取运行时间
        local uptime=$(echo "$sys_output" | grep "System uptime:" | cut -d':' -f2- | xargs)

        # 提取软件序列号
        local software_sn=$(echo "$sys_output" | grep "Software S/N" | sed 's/.*Software S\/N[[:space:]]*:[[:space:]]*//' | xargs)

        echo "│   ├── 系统版本："
        echo "│   │   ├── 防火墙版本：${fw_version:-"Unknown"}"
        echo "│   │   ├── 固件版本：${firmware_version:-"Unknown"}"
        echo "│   │   └── 平台：${platform:-"Unknown"}"
        echo "│   ├── 系统状态："
        echo "│   │   └── 运行时间：${uptime:-"Unknown"}"
        echo "│   └── 软件序列号：${software_sn:-"Unknown"}"
    else
        # Linux系统的系统信息格式 - 从原始数据中提取
        # 提取系统版本信息
        local hostname_name="Unknown"
        local os_name="Unknown"
        local kernel_version="Unknown"
        local architecture="Unknown"

        # 从uname输出中提取信息
        if echo "$sys_output" | grep -q "Linux"; then
            local uname_line=$(echo "$sys_output" | grep "Linux" | head -1)
            hostname_name=$(echo "$uname_line" | awk '{print $2}')
            kernel_version=$(echo "$uname_line" | awk '{print $3}')
            architecture=$(echo "$uname_line" | awk '{print $NF}')
        fi

        # 从os-release输出中提取发行版信息
        if echo "$sys_output" | grep -q "PRETTY_NAME="; then
            os_name=$(echo "$sys_output" | grep "PRETTY_NAME=" | cut -d'=' -f2 | sed 's/"//g')
        elif echo "$sys_output" | grep -q "NAME="; then
            os_name=$(echo "$sys_output" | grep "NAME=" | head -1 | cut -d'=' -f2 | sed 's/"//g')
        fi

        # 提取系统状态信息
        local load_avg="Unknown"
        local uptime_info="Unknown"
        local mem_usage="Unknown"

        # 从uptime输出中提取负载和运行时间
        if echo "$sys_output" | grep -q "load average:"; then
            local uptime_line=$(echo "$sys_output" | grep "load average:" | head -1)
            load_avg=$(echo "$uptime_line" | grep -o "load average:.*" | cut -d':' -f2 | sed 's/^[[:space:]]*//')
            uptime_info=$(echo "$uptime_line" | sed 's/,.*load average.*//' | sed 's/^[[:space:]]*//')
        fi

        # 从free输出中提取内存使用率
        if echo "$sys_output" | grep -q "Mem:"; then
            local mem_line=$(echo "$sys_output" | grep "Mem:" | head -1)
            local total_mem=$(echo "$mem_line" | awk '{print $2}')
            local used_mem=$(echo "$mem_line" | awk '{print $3}')
            if [[ "$total_mem" -gt 0 ]] 2>/dev/null; then
                local usage_percent=$(echo "scale=1; $used_mem * 100 / $total_mem" | bc 2>/dev/null || echo "$(($used_mem * 100 / $total_mem))")
                mem_usage="${usage_percent}%"
            fi
        fi

        echo "│   ├── 系统版本："
        echo "│   │   ├── 主机名称：${hostname_name}"
        echo "│   │   ├── 发行版本：${os_name}"
        echo "│   │   ├── 内核版本：${kernel_version}"
        echo "│   │   └── 系统架构：${architecture}"
        echo "│   └── 系统状态："
        echo "│       ├── 系统负载：${load_avg}"
        echo "│       ├── 内存使用率：${mem_usage}"
        echo "│       └── 运行时间：${uptime_info}"
    fi
}

# 提取N1节点（AFW3000防火墙设备）软件检测详细信息
extract_n1_software_details() {
    local node="$1"
    local soft_output="$2"

    # 解析Web服务检测结果
    local web_status="异常"
    local access_url="Unknown"
    local response_time="Unknown"
    local login_status="Unknown"

    # 从软件检测输出中提取Web服务信息
    # 查找Web服务状态
    if echo "$soft_output" | grep -q "Web服务状态:"; then
        web_status=$(echo "$soft_output" | grep "Web服务状态:" | sed 's/.*Web服务状态:[[:space:]]*//' | xargs)
        # 将状态标准化
        case "$web_status" in
            "正常"|"正常运行"|"运行中"|"active"|"running")
                web_status="正常运行"
                ;;
            *)
                web_status="异常"
                ;;
        esac
    fi

    # 查找访问地址
    if echo "$soft_output" | grep -q "访问地址:"; then
        access_url=$(echo "$soft_output" | grep "访问地址:" | sed 's/.*访问地址:[[:space:]]*//' | xargs)
    fi

    # 查找响应时间
    if echo "$soft_output" | grep -q "响应时间:"; then
        response_time=$(echo "$soft_output" | grep "响应时间:" | sed 's/.*响应时间:[[:space:]]*//' | xargs)
    fi

    # 查找登录页面状态
    if echo "$soft_output" | grep -q "登录页面:"; then
        login_status=$(echo "$soft_output" | grep "登录页面:" | sed 's/.*登录页面:[[:space:]]*//' | xargs)
        # 将状态标准化
        case "$login_status" in
            "正常"|"可访问"|"登录成功"|"accessible"|"success")
                login_status="登录成功"
                ;;
            *)
                login_status="无法访问"
                ;;
        esac
    fi

    # 输出符合设计规范的Web服务信息格式
    echo "    └── Web服务："
    echo "        ├── 服务状态：${web_status}"
    echo "        ├── 访问地址：${access_url}"
    echo "        ├── 响应时间：${response_time}"
    echo "        └── 模拟登录状态：${login_status}"
}

# 提取远程软件检测详细信息（用于N2/N3节点）
extract_remote_software_details() {
    local node="$1"
    local soft_output="$2"

    # 解析软件检测输出，提取统计信息
    local service_count=0
    local security_suite_success=0
    local security_suite_failed=0
    local hardening_status="未知"
    local other_software_success=0
    local other_software_failed=0

    # 从软件检测输出中统计服务数量
    # 查找服务相关的行，统计检测到的服务数量
    if echo "$soft_output" | grep -q "检测服务:"; then
        # 提取检测服务行的内容，统计服务数量
        local service_line=$(echo "$soft_output" | grep "检测服务:" | head -1)
        # 统计服务名称的数量（通过空格分隔的服务名计算）
        service_count=$(echo "$service_line" | grep -o '[a-zA-Z_][a-zA-Z0-9_]*' | wc -l)
        # 减去"检测服务"这两个词
        service_count=$((service_count - 2))
        if [[ $service_count -lt 0 ]]; then
            service_count=0
        fi
    fi

    # 解析安全套件信息
    if echo "$soft_output" | grep -q "security_suite"; then
        security_suite_success=$(echo "$soft_output" | grep -o "security_suite" | wc -l)
    fi

    # 解析安全加固信息 - 修复：检查实际执行状态而不仅仅是关键字
    if echo "$soft_output" | grep -A 5 "security_hardening" | grep -q "no_records"; then
        hardening_status="未执行"
    elif echo "$soft_output" | grep -A 5 "security_hardening" | grep -q "状态: 未安装"; then
        hardening_status="未执行"
    elif echo "$soft_output" | grep -A 5 "security_hardening" | grep -q "状态: 运行中"; then
        hardening_status="已执行加固"
    elif echo "$soft_output" | grep -q "security_hardening"; then
        # 如果找到security_hardening但没有明确状态，检查是否有执行记录
        if echo "$soft_output" | grep -A 10 "security_hardening" | grep -q "has_records"; then
            hardening_status="已执行加固"
        else
            hardening_status="未执行"
        fi
    else
        hardening_status="未执行"
    fi

    # 解析其他软件信息
    if echo "$soft_output" | grep -q "other_software"; then
        other_software_success=$(echo "$soft_output" | grep -o "other_software" | wc -l)
    fi

    echo "    ├── 服务统计："
    echo "    │   ├── 检测服务：${service_count}"
    echo "    │   ├── 安装成功：${security_suite_success}"
    echo "    │   └── 安装失败：${security_suite_failed}"
    # 解析安全套件详细信息
    local security_success_details=""
    local security_failed_details=""

    # 解析每个安全套件服务的状态
    local current_service=""
    local current_status=""
    while IFS= read -r line; do
        if [[ "$line" =~ 检测服务:.*security_suite ]]; then
            current_service=$(echo "$line" | sed 's/检测服务: \([^(]*\).*/\1/' | xargs)
            local service_desc=$(echo "$line" | sed 's/.*(\([^|]*\)|security_suite.*/\1/')
            current_status=""
        elif [[ -n "$current_service" && "$line" =~ 状态: ]]; then
            current_status=$(echo "$line" | sed 's/.*状态: \(.*\)/\1/' | xargs)
            if [[ "$current_status" == "运行中" ]]; then
                security_success_details+="    │   │   ├── $service_desc (运行中)"$'\n'
            elif [[ "$current_status" == "未安装" ]]; then
                security_failed_details+="    │       ├── $service_desc (未安装)"$'\n'
            fi
            current_service=""
        fi
    done <<<"$soft_output"

    echo "    ├── 安全套件："
    echo "    │   ├── 安装成功："
    if [[ -n "$security_success_details" ]]; then
        echo -n "$security_success_details" | sed '$s/├──/└──/'
    else
        echo "    │   │   └── 无"
    fi
    echo "    │   └── 安装失败："
    if [[ -n "$security_failed_details" ]]; then
        echo -n "$security_failed_details" | sed '$s/├──/└──/'
    else
        echo "    │       └── 无"
    fi
    echo "    ├── 安全加固：${hardening_status}"
    echo "    └── 其他软件："
    echo "        ├── 安装成功："
    if [[ $other_software_success -gt 0 ]]; then
        echo "        │   └── 系统服务等 ${other_software_success} 个"
    else
        echo "        │   └── 无"
    fi
    echo "        └── 安装失败："
    echo "            └── 无"
}

# 处理远程设备检测数据
process_remote_devices_data() {
    local target_nodes="$1"

    for node in $target_nodes; do
        local node_upper=$(echo "$node" | tr '[:lower:]' '[:upper:]')

        # 获取节点配置信息
        local node_config
        node_config=$(get_device_config "$node" 2>/dev/null || echo "Unknown Unknown Unknown Unknown Unknown Unknown")
        local host user password port backup_user backup_password
        read -r host user password port backup_user backup_password <<<"$node_config"

        echo "[${node_upper}节点 - $host]"

        # 动态查找网络检测结果文件（选择最新的）
        local temp_result_file=$(ls -t /tmp/afw3000_network_result_${node}_* 2>/dev/null | head -1)

        # 检查是否有网络检测结果
        if [[ -f "$temp_result_file" ]]; then
            # 读取网络检测结果
            local exit_code=$(grep "NETWORK_CHECK_EXIT_CODE=" "$temp_result_file" | cut -d'=' -f2)
            local output_content=$(sed -n '/NETWORK_CHECK_OUTPUT_START/,/NETWORK_CHECK_OUTPUT_END/p' "$temp_result_file" | sed '1d;$d')

            if [[ "$exit_code" -eq 0 ]]; then
                # 处理成功的检测结果
                process_successful_remote_detection "$node" "$output_content"
            else
                # 处理失败的检测结果
                echo "├── 网络检测：异常"
                echo "│   └── 连接失败：无法建立网络连接"
                echo "├── 硬件信息：跳过"
                echo "│   └── 原因：网络检测失败"
                echo "├── 系统信息：跳过"
                echo "│   └── 原因：网络检测失败"
                echo "└── 软件信息：跳过"
                echo "    └── 原因：网络检测失败"
            fi
        else
            # 没有检测结果文件
            echo "├── 网络检测：异常"
            echo "│   └── 连接失败：检测未执行"
            echo "├── 硬件信息：跳过"
            echo "│   └── 原因：网络检测失败"
            echo "├── 系统信息：跳过"
            echo "│   └── 原因：网络检测失败"
            echo "└── 软件信息：跳过"
            echo "    └── 原因：网络检测失败"
        fi

        echo ""

        # 清理临时文件
        rm -f "$temp_result_file"
        rm -f /tmp/afw3000_hardware_result_${node}_*
        rm -f /tmp/afw3000_system_result_${node}_*
        rm -f /tmp/afw3000_software_result_${node}_*
    done
}

# 处理成功的远程检测结果
process_successful_remote_detection() {
    local node="$1"
    local output_content="$2"

    # 解析网络检测结果
    local network_status="正常"
    local port_results=""

    # 从输出中提取端口检测结果
    local port_lines=()
    while IFS= read -r line; do
        if [[ "$line" =~ 端口\ ([0-9]+):\ (.+) ]]; then
            local port_num="${BASH_REMATCH[1]}"
            local port_status="${BASH_REMATCH[2]}"
            if [[ "$port_status" == *"开放"* ]]; then
                port_lines+=("│   ├── 端口${port_num}：开放")
            else
                port_lines+=("│   ├── 端口${port_num}：关闭")
            fi
        fi
    done <<<"$output_content"

    # 构建端口结果，最后一个使用└──
    for i in "${!port_lines[@]}"; do
        if [[ $i -eq $((${#port_lines[@]} - 1)) ]]; then
            # 最后一个端口使用└──
            port_results+="${port_lines[i]/├──/└──}"$'\n'
        else
            port_results+="${port_lines[i]}"$'\n'
        fi
    done

    # 如果没有提取到端口信息，使用默认格式
    if [[ -z "$port_results" ]]; then
        port_results="│   └── SSH连接：成功"
    else
        # 将最后一个├──改为└──，并确保末尾没有换行
        port_results=$(echo "$port_results" | sed '$s/├──/└──/' | sed '$s/$//')
    fi

    # 处理硬件、系统、软件检测结果
    local hardware_status="模块开发中"
    local hardware_details="│   └── 状态：模块开发中"
    local system_status="模块开发中"
    local system_details="│   └── 状态：模块开发中"
    local software_status="模块开发中"
    local software_details="    └── 状态：模块开发中"

    # 检查硬件检测结果（选择最新的）
    local temp_hardware_file=$(ls -t /tmp/afw3000_hardware_result_${node}_* 2>/dev/null | head -1)
    if [[ -f "$temp_hardware_file" ]]; then
        local hw_exit_code=$(grep "HARDWARE_CHECK_EXIT_CODE=" "$temp_hardware_file" | cut -d'=' -f2)
        if [[ "$hw_exit_code" -eq 0 ]]; then
            hardware_status="正常"
            # 修复：从原始数据文件中读取真正的命令输出
            local raw_data_dir=""
            # 优先使用测试环境变量
            if [[ -n "$MOCK_RAW_DATA_DIR" ]]; then
                raw_data_dir="$MOCK_RAW_DATA_DIR"
            else
                raw_data_dir=$(find /tmp -name "afw3000_raw_data_*" -type d | head -1)
            fi

            if [[ "$node" == "n1" ]]; then
                # N1节点：从原始数据文件中获取show interface命令的真实输出
                local hw_output=""
                if [[ -n "$raw_data_dir" && -f "$raw_data_dir/network_${node}.txt" ]]; then
                    # 从网络原始数据文件中提取show interface输出
                    hw_output=$(sed -n '/## show interface输出:/,/^========================================$/p' "$raw_data_dir/network_${node}.txt" | sed '1d;$d' | grep -v "INFO\|ERROR\|WARN")
                fi

                # 如果原始数据不可用，降级使用检测结果文件
                if [[ -z "$hw_output" ]]; then
                    hw_output=$(sed -n '/HARDWARE_CHECK_OUTPUT_START/,/HARDWARE_CHECK_OUTPUT_END/p' "$temp_hardware_file" | sed '1d;$d')
                fi

                hardware_details=$(extract_remote_hardware_details "$node" "$hw_output")
            else
                # N2/N3节点（Linux系统）使用与本地检测相同的解析逻辑
                hardware_details=$(extract_hardware_details "$temp_hardware_file")
            fi
        else
            hardware_status="异常"
            hardware_details="│   └── 检测失败：硬件信息获取失败"
        fi
    fi

    # 检查系统检测结果（选择最新的）
    local temp_system_file=$(ls -t /tmp/afw3000_system_result_${node}_* 2>/dev/null | head -1)
    if [[ -f "$temp_system_file" ]]; then
        local sys_exit_code=$(grep "SYSTEM_CHECK_EXIT_CODE=" "$temp_system_file" | cut -d'=' -f2)
        if [[ "$sys_exit_code" -eq 0 ]]; then
            system_status="正常"
            # 修复：从原始数据文件中读取真正的命令输出
            local raw_data_dir=""
            # 优先使用测试环境变量
            if [[ -n "$MOCK_RAW_DATA_DIR" ]]; then
                raw_data_dir="$MOCK_RAW_DATA_DIR"
            else
                raw_data_dir=$(find /tmp -name "afw3000_raw_data_*" -type d | head -1)
            fi

            if [[ "$node" == "n1" ]]; then
                # N1节点：从原始数据文件中获取show ver命令的真实输出
                local sys_output=""
                if [[ -n "$raw_data_dir" && -f "$raw_data_dir/system_${node}.txt" ]]; then
                    # 从系统原始数据文件中提取show ver输出
                    sys_output=$(sed -n '/## show ver输出:/,/^========================================$/p' "$raw_data_dir/system_${node}.txt" | sed '1d;$d' | grep -v "INFO\|ERROR\|WARN")
                fi

                # 如果原始数据不可用，降级使用检测结果文件
                if [[ -z "$sys_output" ]]; then
                    sys_output=$(sed -n '/SYSTEM_CHECK_OUTPUT_START/,/SYSTEM_CHECK_OUTPUT_END/p' "$temp_system_file" | sed '1d;$d')
                fi

                system_details=$(extract_remote_system_details "$node" "$sys_output")
            else
                # N2/N3节点使用与本地检测相同的解析逻辑
                system_details=$(extract_system_details "$temp_system_file")
            fi
        else
            system_status="异常"
            system_details="│   └── 检测失败：系统信息获取失败"
        fi
    fi

    # 检查软件检测结果（选择最新的）
    local temp_software_file=$(ls -t /tmp/afw3000_software_result_${node}_* 2>/dev/null | head -1)
    if [[ -f "$temp_software_file" ]]; then
        local soft_exit_code=$(grep "SOFTWARE_CHECK_EXIT_CODE=" "$temp_software_file" | cut -d'=' -f2)
        if [[ "$soft_exit_code" -eq 0 ]]; then
            software_status="正常"
            # 修复：根据节点类型使用不同的解析逻辑
            if [[ "$node" == "n1" ]]; then
                # N1节点（AFW3000防火墙设备）使用专门的Web服务解析逻辑
                local soft_output=$(sed -n '/SOFTWARE_CHECK_OUTPUT_START/,/SOFTWARE_CHECK_OUTPUT_END/p' "$temp_software_file" | sed '1d;$d')
                software_details=$(extract_n1_software_details "$node" "$soft_output")
            else
                # N2/N3节点（Linux系统）使用与本地检测相同的软件信息解析逻辑
                software_details=$(extract_software_details "$temp_software_file")
            fi
        else
            software_status="异常"
            software_details="    └── 检测失败：软件信息获取失败"
        fi
    fi

    # 输出检测结果
    echo "├── 网络检测：$network_status"
    echo "$port_results"
    echo "├── 硬件信息：$hardware_status"
    echo "$hardware_details"
    echo "├── 系统信息：$system_status"
    echo "$system_details"
    echo "└── 软件信息：$software_status"
    echo "$software_details"
}

# 处理原始数据附录
process_raw_data_appendix() {
    local detection_mode="$1"
    local target_nodes="$2"
    local raw_data_dir="$3"

    if [[ "$detection_mode" == "local" ]]; then
        # 本地检测模式 - 合并所有原始数据到一个文件
        local combined_raw_file="/tmp/combined_raw_localhost_$$"

        # 合并硬件、系统、网络原始数据
        {
            echo "硬件配置原始数据"
            echo "--------------"
            cat "$raw_data_dir/hardware_localhost.txt" 2>/dev/null || echo "无硬件配置数据"
            echo ""
            echo "系统信息原始数据"
            echo "--------------"
            cat "$raw_data_dir/system_localhost.txt" 2>/dev/null || echo "无系统信息数据"
            echo ""
            echo "网络配置原始数据"
            echo "--------------"
            cat "$raw_data_dir/network_localhost.txt" 2>/dev/null || echo "无网络配置数据"
        } > "$combined_raw_file"

        # 格式化原始数据输出
        format_raw_output "localhost" "$combined_raw_file"

        # 清理临时文件
        rm -f "$combined_raw_file"
    else
        # 远程检测模式
        for node in $target_nodes; do
            # 合并节点的所有原始数据
            local combined_raw_file="/tmp/combined_raw_${node}_$$"

            {
                echo "硬件配置原始数据"
                echo "--------------"
                cat "$raw_data_dir/hardware_${node}.txt" 2>/dev/null || echo "无硬件配置数据"
                echo ""
                echo "系统信息原始数据"
                echo "--------------"
                cat "$raw_data_dir/system_${node}.txt" 2>/dev/null || echo "无系统信息数据"
                echo ""
                echo "网络配置原始数据"
                echo "--------------"
                cat "$raw_data_dir/network_${node}.txt" 2>/dev/null || echo "无网络配置数据"
            } > "$combined_raw_file"

            # 格式化原始数据输出
            format_raw_output "$node" "$combined_raw_file"

            # 清理临时文件
            rm -f "$combined_raw_file"
        done
    fi
}

# =============================================================================
# 模块接口函数
# =============================================================================

# 处理本地检测数据并生成完整报告
process_local_detection() {
    local detection_mode="$1"
    local target_info="$2"
    local script_version="$3"
    local raw_data_dir="$4"
    local temp_files_prefix="$5"

    # 生成报告头部
    generate_report_header "$detection_mode" "$target_info" "$script_version"

    # 生成设备概况（本地检测固定为1个节点）
    generate_device_summary "1" "1" "0" "100.0%"

    # 生成详细检测结果头部
    generate_detailed_results_header

    # 处理本地检测数据
    process_local_device_data "$temp_files_prefix"

    # 生成原始数据附录
    generate_raw_output_header
    process_local_raw_data "$raw_data_dir"

    # 生成报告尾部
    generate_report_footer
}

# 处理本地设备检测数据
process_local_device_data() {
    local temp_files_prefix="$1"
    local hostname=$(hostname)
    local local_ip=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}' 2>/dev/null || echo "127.0.0.1")

    # 处理网络检测结果
    local network_status="正常"
    local network_details=""

    # 检测本地网络端口
    local ssh_port_status="关闭"
    local http_port_status="关闭"
    local https_port_status="关闭"

    # 检测SSH端口22
    if netstat -tuln 2>/dev/null | grep -q ":22 "; then
        ssh_port_status="开放"
    fi

    # 检测HTTP端口80
    if netstat -tuln 2>/dev/null | grep -q ":80 "; then
        http_port_status="开放"
    fi

    # 检测HTTPS端口443
    if netstat -tuln 2>/dev/null | grep -q ":443 "; then
        https_port_status="开放"
    fi

    # 构建网络详细信息
    network_details="│   ├── 端口22：$ssh_port_status"$'\n'
    network_details+="│   ├── 端口80：$http_port_status"$'\n'
    network_details+="│   └── 端口443：$https_port_status"

    # 处理硬件检测结果
    local hardware_status="正常"
    local hardware_details=""

    # 查找硬件检测结果文件（选择最新的）
    local temp_hardware_file=$(ls -t /tmp/afw3000_hardware_result_localhost_* 2>/dev/null | head -1)

    if [[ -f "$temp_hardware_file" ]]; then
        hardware_details=$(extract_hardware_details "$temp_hardware_file")
    else
        hardware_status="未检测"
        hardware_details="│   └── 本地检测未完成"
    fi

    # 处理系统检测结果
    local system_status="正常"
    local system_details=""

    # 查找系统检测结果文件（选择最新的）
    local temp_system_file=$(ls -t /tmp/afw3000_system_result_localhost_* 2>/dev/null | head -1)

    if [[ -f "$temp_system_file" ]]; then
        system_details=$(extract_system_details "$temp_system_file")
    else
        system_status="未检测"
        system_details="│   └── 本地检测未完成"
    fi

    # 处理软件检测结果
    local software_status="正常"
    local software_details=""

    # 查找软件检测结果文件（选择最新的）
    local temp_software_file=$(ls -t /tmp/afw3000_software_result_localhost_* 2>/dev/null | head -1)

    if [[ -f "$temp_software_file" ]]; then
        software_details=$(extract_software_details "$temp_software_file")
    else
        software_status="未检测"
        software_details="    └── 本地检测未完成"
    fi

    # 生成本地设备检测结果
    format_local_device_result "$hostname" "$local_ip" "$network_status" "$network_details" "$hardware_status" "$hardware_details" "$system_status" "$system_details" "$software_status" "$software_details"
}

# 生成完整的检测报告（主入口接口）
generate_full_report() {
    local detection_mode="$1"      # local/remote
    local target_nodes="$2"        # 目标节点列表
    local script_version="$3"      # 脚本版本
    local start_time="$4"          # 开始时间
    local raw_data_dir="$5"        # 原始数据目录
    local report_dir="$6"          # 报告输出目录
    local report_tag="$7"          # 报告标识符（可选）

    # 生成报告文件名
    local report_filename
    if [[ "$detection_mode" == "local" ]]; then
        if [[ -n "$report_tag" ]]; then
            report_filename="afw3000_local_check_${start_time}_${report_tag}.txt"
        else
            report_filename="afw3000_local_check_${start_time}.txt"
        fi
    else
        if [[ -n "$report_tag" ]]; then
            report_filename="afw3000_check_${start_time}_${report_tag}.txt"
        else
            report_filename="afw3000_check_${start_time}.txt"
        fi
    fi
    local report_file="$report_dir/$report_filename"

    # 确定检测范围描述
    local scope_desc
    if [[ "$detection_mode" == "local" ]]; then
        scope_desc="$(hostname)"
    else
        case "$target_nodes" in
        "n1 n2 n3") scope_desc="所有节点 (N1, N2, N3)" ;;
        "n1") scope_desc="N1节点" ;;
        "n2") scope_desc="N2节点" ;;
        "n3") scope_desc="N3节点" ;;
        *) scope_desc="$target_nodes" ;;
        esac
    fi

    # 生成报告头部
    generate_report_header "$detection_mode" "$scope_desc" "$script_version" > "$report_file"

    # 统计检测结果
    local total_nodes=0
    local success_nodes=0
    local failed_nodes=0

    if [[ "$detection_mode" == "local" ]]; then
        # 本地检测模式统计
        total_nodes=1
        # 检查本地检测是否成功（基于硬件检测结果）
        local temp_hardware_file=$(ls -t /tmp/afw3000_hardware_result_localhost_* 2>/dev/null | head -1)
        if [[ -f "$temp_hardware_file" ]]; then
            local exit_code=$(grep "HARDWARE_CHECK_EXIT_CODE=" "$temp_hardware_file" | cut -d'=' -f2)
            if [[ "$exit_code" -eq 0 ]]; then
                success_nodes=1
            else
                failed_nodes=1
            fi
        else
            failed_nodes=1
        fi
    else
        # 远程检测模式统计
        for node in $target_nodes; do
            total_nodes=$((total_nodes + 1))
            local temp_result_file=$(ls -t /tmp/afw3000_network_result_${node}_* 2>/dev/null | head -1)
            if [[ -f "$temp_result_file" ]]; then
                local exit_code=$(grep "NETWORK_CHECK_EXIT_CODE=" "$temp_result_file" | cut -d'=' -f2)
                if [[ "$exit_code" -eq 0 ]]; then
                    success_nodes=$((success_nodes + 1))
                else
                    failed_nodes=$((failed_nodes + 1))
                fi
            else
                failed_nodes=$((failed_nodes + 1))
            fi
        done
    fi

    # 计算成功率
    local success_rate=0
    if [[ $total_nodes -gt 0 ]]; then
        success_rate=$(echo "scale=1; $success_nodes * 100 / $total_nodes" | bc 2>/dev/null || echo "$(($success_nodes * 100 / $total_nodes))")
    fi

    # 生成设备概况
    generate_device_summary "$total_nodes" "$success_nodes" "$failed_nodes" "${success_rate}%" >> "$report_file"

    # 生成详细检测结果头部
    echo "========================================" >> "$report_file"
    echo "设备详细检测结果" >> "$report_file"
    echo "========================================" >> "$report_file"
    echo "" >> "$report_file"

    # 生成设备检测结果
    if [[ "$detection_mode" == "local" ]]; then
        # 本地检测模式
        process_local_device_data "/tmp/afw3000" >> "$report_file"
    else
        # 远程检测模式
        process_remote_devices_data "$target_nodes" >> "$report_file"
    fi

    # 生成原始数据附录
    echo "" >> "$report_file"
    generate_raw_output_header >> "$report_file"
    process_raw_data_appendix "$detection_mode" "$target_nodes" "$raw_data_dir" >> "$report_file"

    # 生成报告尾部
    echo "================================================================================" >> "$report_file"
    echo "附录结束" >> "$report_file"
    echo "================================================================================" >> "$report_file"
    echo "" >> "$report_file"
    echo "报告生成时间：$(date '+%Y-%m-%d %H:%M:%S')" >> "$report_file"

    # 输出报告文件路径
    echo "$report_file"
}

# 主接口函数
main() {
    local action="$1"
    shift

    case "$action" in
        "header")
            generate_report_header "$@"
            ;;
        "summary")
            generate_device_summary "$@"
            ;;
        "device")
            format_device_result "$@"
            ;;
        "local-device")
            format_local_device_result "$@"
            ;;
        "network-details")
            format_network_details "$@"
            ;;
        "raw")
            format_raw_output "$@"
            ;;
        "complete")
            generate_complete_report "$@"
            ;;
        "simple")
            generate_simple_report "$@"
            ;;
        "process-local")
            process_local_detection "$@"
            ;;
        "process-local-device")
            process_local_device_data "$@"
            ;;
        "full-report")
            generate_full_report "$@"
            ;;
        *)
            echo "错误: 未知的操作 '$action'"
            echo "用法: $0 <action> [参数...]"
            echo "支持的操作: header, summary, device, local-device, network-details, raw, complete, simple, process-local, process-local-device, full-report"
            exit 1
            ;;
    esac
}

# 如果直接执行此脚本，调用主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
