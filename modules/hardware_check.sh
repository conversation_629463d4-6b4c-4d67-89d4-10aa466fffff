#!/bin/bash
# 硬件信息检测模块
# 功能: 主板、CPU、内存、存储、网口信息检测
# 版本: 1.0

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

# =============================================================================
# 模块信息
# =============================================================================

MODULE_NAME="硬件信息检测模块"
MODULE_VERSION="1.0"

# =============================================================================
# 参数验证和解析
# =============================================================================

validate_parameters() {
    # 本地检测模式：检查是否为localhost
    if [[ "$1" == "localhost" ]]; then
        return 0
    fi

    # 远程检测模式：原有参数验证逻辑
    if [[ $# -ne 4 ]]; then
        log_error "参数错误"
        echo "用法: $0 <设备名称> <设备IP> <SSH用户名> <SSH密码>"
        echo "示例: $0 n1 ************* admin admin@123"
        return 1
    fi

    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"

    # 验证设备名称
    if [[ ! "$device_name" =~ ^(n1|n2|n3)$ ]]; then
        log_error "无效的设备名称: $device_name (支持: n1, n2, n3)"
        return 1
    fi

    # 验证IP地址格式
    if [[ ! "$device_ip" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        log_error "无效的IP地址格式: $device_ip"
        return 1
    fi

    # 验证用户名和密码非空
    if [[ -z "$ssh_user" ]]; then
        log_error "SSH用户名不能为空"
        return 1
    fi

    if [[ -z "$ssh_password" ]]; then
        log_error "SSH密码不能为空"
        return 1
    fi

    return 0
}

# =============================================================================
# 硬件检测功能
# =============================================================================

# 主板信息检测
get_motherboard_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo "=========================================="
    echo "主板信息检测"
    echo "=========================================="

    # 声明变量
    local mb_info=""
    local mb_status=1

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "设备: localhost (本地主机)"
        echo ""

        log_info "开始主板信息检测..."

        # 直接执行本地命令 - 使用与调试脚本完全相同的命令
        mb_info=$(dmidecode -t baseboard 2>/dev/null && echo -e "\n=== dmidecode -t system ===" && dmidecode -t system 2>/dev/null && echo -e "\n=== dmidecode -t bios ===" && dmidecode -t bios 2>/dev/null)
        mb_status=$?  # 修复：保存命令退出状态

        # 添加调试信息
        echo "DEBUG: 本地主板检测 - 退出状态: $mb_status, 信息长度: ${#mb_info}" >&2
        if [[ ${#mb_info} -gt 0 ]]; then
            echo "DEBUG: 信息预览: $(echo "$mb_info" | head -3 | tr '\n' ' ')" >&2
        fi
    else
        # 远程检测模式（保持原有逻辑）
        echo "设备: $device_name ($device_ip:$ssh_port)"
        echo ""

        log_info "开始主板信息检测..."

        # 根据设备类型使用不同的命令
        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令获取硬件信息
            mb_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
            mb_status=$?  # 修复：保存命令退出状态
        else
            # Linux系统 - 使用与原始数据收集相同的命令
            mb_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "dmidecode -t baseboard 2>/dev/null && echo -e '\n=== dmidecode -t system ===' && dmidecode -t system 2>/dev/null && echo -e '\n=== dmidecode -t bios ===' && dmidecode -t bios 2>/dev/null" 2>/dev/null)
            mb_status=$?  # 修复：保存命令退出状态
        fi

        # 添加调试信息
        echo "DEBUG: 远程主板检测 - 退出状态: $mb_status, 信息长度: ${#mb_info}" >&2
    fi

    echo "DEBUG: 条件判断 - mb_status=$mb_status, mb_info非空=$([ -n "$mb_info" ] && echo "是" || echo "否")" >&2
    if [[ $mb_status -eq 0 && -n "$mb_info" ]]; then
        echo "主板信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中动态提取信息
            local mb_data=$(echo "$mb_info" | grep -v "INFO\|ERROR\|WARN")
            local platform_info=$(echo "$mb_data" | grep "Platform" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local model_info=$(echo "$mb_data" | grep "Model" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local product_info=$(echo "$mb_data" | grep "Product" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  平台: ${platform_info:-"-"}"
            echo "  型号: ${model_info:-"-"}"
            echo "  产品: ${product_info:-"-"}"
        else
            # Linux系统从dmidecode输出中提取主板信息
            local mb_manufacturer=$(echo "$mb_info" | grep "Manufacturer:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local mb_product=$(echo "$mb_info" | grep "Product Name:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local mb_version=$(echo "$mb_info" | grep "Version:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  制造商: ${mb_manufacturer:-"Unknown"}"
            echo "  型号: ${mb_product:-"Unknown"}"
            echo "  版本: ${mb_version:-"Unknown"}"
        fi
        log_info "主板信息检测成功"
        return 0
    else
        echo "主板信息获取失败"
        log_error "主板信息检测失败"
        return 1
    fi
}

# CPU信息检测
get_cpu_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "CPU信息检测"
    echo "=========================================="

    # 声明变量
    local cpu_info=""
    local cpu_status=1

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始CPU信息检测..."

        # 直接执行本地命令 - 使用与调试脚本完全相同的命令
        cpu_info=$(lscpu 2>/dev/null && echo -e "\n=== /proc/cpuinfo ===" && cat /proc/cpuinfo 2>/dev/null)
        cpu_status=$?  # 修复：保存命令退出状态

        # 添加调试信息
        echo "DEBUG: 本地CPU检测 - 退出状态: $cpu_status, 信息长度: ${#cpu_info}" >&2
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始CPU信息检测..."

        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令
            cpu_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
            cpu_status=$?  # 修复：保存命令退出状态
        else
            # Linux系统 - 使用与原始数据收集相同的命令
            cpu_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "lscpu 2>/dev/null && echo -e '\n=== /proc/cpuinfo ===' && cat /proc/cpuinfo 2>/dev/null" 2>/dev/null)
            cpu_status=$?  # 修复：保存命令退出状态
        fi

        # 添加调试信息
        echo "DEBUG: 远程CPU检测 - 退出状态: $cpu_status, 信息长度: ${#cpu_info}" >&2
    fi

    echo "DEBUG: CPU条件判断 - cpu_status=$cpu_status, cpu_info非空=$([ -n "$cpu_info" ] && echo "是" || echo "否")" >&2
    if [[ $cpu_status -eq 0 && -n "$cpu_info" ]]; then
        echo "CPU信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中尝试提取CPU相关信息
            local cpu_data=$(echo "$cpu_info" | grep -v "INFO\|ERROR\|WARN")

            # 尝试从输出中查找CPU相关信息（通常AFW3000的show ver不包含详细CPU信息）
            local cpu_model=$(echo "$cpu_data" | grep -i "cpu\|processor" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local cpu_cores=$(echo "$cpu_data" | grep -i "core\|核" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local cpu_freq=$(echo "$cpu_data" | grep -i "freq\|mhz\|频率" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  型号: ${cpu_model:-"-"}"
            echo "  核数: ${cpu_cores:-"-"}"
            echo "  频率: ${cpu_freq:-"-"}"
        else
            # 从lscpu输出中提取CPU信息，过滤掉日志行
            local cpu_data=$(echo "$cpu_info" | grep -v "INFO\|ERROR\|WARN")

            # 优先从lscpu输出中获取CPU信息
            local cpu_model=$(echo "$cpu_data" | grep "Model name:" | head -1 | cut -d':' -f2- | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local cpu_cores=$(echo "$cpu_data" | grep "CPU(s):" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local cpu_freq=$(echo "$cpu_data" | grep "CPU MHz:" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            # 如果lscpu没有提供完整信息，从/proc/cpuinfo中获取
            if [[ -z "$cpu_model" ]]; then
                cpu_model=$(echo "$cpu_data" | grep "model name" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            fi

            if [[ -z "$cpu_cores" ]]; then
                # 从/proc/cpuinfo统计processor数量
                cpu_cores=$(echo "$cpu_data" | grep "processor" | wc -l)
            fi

            if [[ -z "$cpu_freq" ]]; then
                # 从/proc/cpuinfo获取频率信息
                cpu_freq=$(echo "$cpu_data" | grep "cpu MHz" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                if [[ -z "$cpu_freq" ]]; then
                    cpu_freq=$(echo "$cpu_data" | grep "BogoMIPS" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
                    if [[ -n "$cpu_freq" ]]; then
                        cpu_freq="${cpu_freq} (BogoMIPS)"
                    fi
                else
                    cpu_freq="${cpu_freq}MHz"
                fi
            else
                cpu_freq="${cpu_freq}MHz"
            fi

            # 显示CPU信息
            echo "  型号: ${cpu_model:-未知}"
            echo "  核数: ${cpu_cores:-未知}"
            echo "  频率: ${cpu_freq:-未知}"
        fi
        log_info "CPU信息检测成功"
        return 0
    else
        echo "CPU信息获取失败"
        log_error "CPU信息检测失败"
        return 1
    fi
}

# 内存信息检测
get_memory_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "内存信息检测"
    echo "=========================================="

    # 声明变量
    local mem_info=""
    local mem_status=1

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始内存信息检测..."

        # 直接执行本地命令 - 使用与调试脚本完全相同的命令
        mem_info=$(free -h 2>/dev/null && echo -e "\n=== dmidecode -t memory ===" && dmidecode -t memory 2>/dev/null && echo -e "\n=== /proc/meminfo ===" && cat /proc/meminfo 2>/dev/null)
        mem_status=$?  # 修复：保存命令退出状态

        # 添加调试信息
        echo "DEBUG: 本地内存检测 - 退出状态: $mem_status, 信息长度: ${#mem_info}" >&2
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始内存信息检测..."

        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令
            mem_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
            mem_status=$?  # 修复：保存命令退出状态
        else
            # Linux系统 - 使用与原始数据收集相同的命令
            mem_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "free -h 2>/dev/null && echo -e '\n=== dmidecode -t memory ===' && dmidecode -t memory 2>/dev/null && echo -e '\n=== /proc/meminfo ===' && cat /proc/meminfo 2>/dev/null" 2>/dev/null)
            mem_status=$?  # 修复：保存命令退出状态
        fi

        # 添加调试信息
        echo "DEBUG: 远程内存检测 - 退出状态: $mem_status, 信息长度: ${#mem_info}" >&2
    fi

    echo "DEBUG: 内存条件判断 - mem_status=$mem_status, mem_info非空=$([ -n "$mem_info" ] && echo "是" || echo "否")" >&2
    if [[ $mem_status -eq 0 && -n "$mem_info" ]]; then
        echo "内存信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中尝试提取内存信息
            local mem_data=$(echo "$mem_info" | grep -v "INFO\|ERROR\|WARN")

            # 尝试从输出中查找内存相关信息（通常AFW3000的show ver不包含详细内存信息）
            local total_mem=$(echo "$mem_data" | grep -i "memory\|mem\|内存" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local mem_type=$(echo "$mem_data" | grep -i "ddr\|ram" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  └── 总容量：${total_mem:-"-"}"
            echo "  内存条列表:"
            if [[ -n "$total_mem" && "$total_mem" != "-" ]]; then
                echo "    └── 内存条1: 型号=${mem_type:-"-"}, 容量=$total_mem, 类型=${mem_type:-"-"}"
            else
                echo "    └── 无内存条信息"
            fi
        else
            # Linux系统从实际输出中提取内存信息
            local mem_data=$(echo "$mem_info" | grep -v "INFO\|ERROR\|WARN")
            local available_mem=$(echo "$mem_data" | grep "MemTotal" | awk '{printf "%.0fMB", $2/1024}' 2>/dev/null)
            local mem_available=$(echo "$mem_data" | grep "MemAvailable" | awk '{printf "%.0fMB", $2/1024}' 2>/dev/null)

            # 尝试从dmidecode获取物理内存容量
            local physical_mem=""
            local dmidecode_data=$(echo "$mem_info" | grep -A 100 "=== Memory Details ===")
            if [[ -n "$dmidecode_data" ]]; then
                # 从dmidecode输出中提取物理内存大小，使用更精确的匹配
                local mem_size=$(echo "$dmidecode_data" | grep -E "^[[:space:]]*Size:[[:space:]]*[0-9]+" | head -1 | sed 's/^[[:space:]]*Size:[[:space:]]*//' | sed 's/[[:space:]]*$//')
                if [[ -n "$mem_size" && "$mem_size" != "No Module Installed" && "$mem_size" != "No" ]]; then
                    physical_mem="$mem_size"
                else
                    # 如果dmidecode解析失败，尝试从内存条列表中提取
                    local mem_from_list=$(echo "$mem_info" | grep -E "容量=[0-9]+ MB" | head -1 | sed 's/.*容量=//' | sed 's/ MB.*//' | sed 's/$/MB/')
                    if [[ -n "$mem_from_list" ]]; then
                        physical_mem="$mem_from_list"
                    else
                        physical_mem="${available_mem:-"-"}"
                    fi
                fi
            else
                # 如果没有dmidecode数据，尝试从内存条列表中提取
                local mem_from_list=$(echo "$mem_info" | grep -E "容量=[0-9]+ MB" | head -1 | sed 's/.*容量=//' | sed 's/ MB.*//' | sed 's/$/MB/')
                if [[ -n "$mem_from_list" ]]; then
                    physical_mem="$mem_from_list"
                else
                    physical_mem="${available_mem:-"-"}"
                fi
            fi

            # 计算内存总容量（从内存条列表中统计）
            local total_memory_capacity=""
            # 从内存条列表中提取容量信息并求和
            local memory_list_data=$(echo "$mem_info" | grep -E "容量=[0-9]+ [GM]B")
            if [[ -n "$memory_list_data" ]]; then
                local total_mb=0
                local memory_count=0
                while IFS= read -r line; do
                    if [[ "$line" =~ 容量=([0-9]+)[[:space:]]*([GM]B) ]]; then
                        local value="${BASH_REMATCH[1]}"
                        local unit="${BASH_REMATCH[2]}"
                        memory_count=$((memory_count + 1))
                        if [[ "$unit" == "GB" ]]; then
                            total_mb=$((total_mb + value * 1024))
                        elif [[ "$unit" == "MB" ]]; then
                            total_mb=$((total_mb + value))
                        fi
                    fi
                done <<<"$memory_list_data"

                # 如果总容量大于1024MB，转换为GB显示
                if [[ "$total_mb" -gt 1024 ]]; then
                    local total_gb=$((total_mb / 1024))
                    total_memory_capacity="${total_gb}GB"
                else
                    total_memory_capacity="${total_mb}MB"
                fi
            else
                total_memory_capacity="${available_mem:-"-"}"
            fi

            echo "  └── 总容量：${total_memory_capacity:-"-"}"
            echo "  内存条列表:"

            # 尝试从dmidecode输出中提取详细内存条信息
            local dmidecode_data=$(echo "$mem_data" | sed -n '/=== Memory Details ===/,$p')
            if [[ -n "$dmidecode_data" ]]; then
                # 解析dmidecode输出中的内存条信息
                local memory_devices=$(echo "$dmidecode_data" | grep -A 20 "Memory Device" | grep -E "Size:|Type:|Speed:|Manufacturer:|Part Number:" | sed 's/^[[:space:]]*//')

                if [[ -n "$memory_devices" ]]; then
                    local device_count=1
                    local current_size=""
                    local current_type=""
                    local current_speed=""
                    local current_manufacturer=""
                    local current_part=""
                    local memory_list=()

                    while IFS= read -r line; do
                        if [[ "$line" =~ Size:[[:space:]]*(.+) ]]; then
                            current_size="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Type:[[:space:]]*(.+) ]]; then
                            current_type="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Speed:[[:space:]]*(.+) ]]; then
                            current_speed="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Manufacturer:[[:space:]]*(.+) ]]; then
                            current_manufacturer="${BASH_REMATCH[1]}"
                        elif [[ "$line" =~ Part\ Number:[[:space:]]*(.+) ]]; then
                            current_part="${BASH_REMATCH[1]}"

                            # 当获取到Part Number时，表示一个内存条信息收集完成
                            if [[ "$current_size" != "No Module Installed" && -n "$current_size" ]]; then
                                memory_list+=("内存条${device_count}: 型号=${current_part:-"-"}, 容量=${current_size:-"-"}, 类型=${current_type:-"-"}, 速度=${current_speed:-"-"}, 厂商=${current_manufacturer:-"-"}")
                                device_count=$((device_count + 1))
                            fi

                            # 重置变量
                            current_size=""
                            current_type=""
                            current_speed=""
                            current_manufacturer=""
                            current_part=""
                        fi
                    done <<<"$memory_devices"

                    # 输出内存条列表，使用正确的树状结构
                    for i in "${!memory_list[@]}"; do
                        if [[ "$i" -eq $((${#memory_list[@]} - 1)) ]]; then
                            echo "    └── ${memory_list[i]}"
                        else
                            echo "    ├── ${memory_list[i]}"
                        fi
                    done

                    # 如果没有找到任何内存条，显示基本信息
                    if [[ "$device_count" -eq 1 ]]; then
                        if [[ -n "$physical_mem" && "$physical_mem" != "-" ]]; then
                            echo "    └── 内存条1: 型号=-, 容量=$physical_mem, 类型=-"
                        else
                            echo "    └── 无内存条信息"
                        fi
                    fi
                else
                    # dmidecode没有返回有效数据，使用基本信息
                    if [[ -n "$physical_mem" && "$physical_mem" != "-" ]]; then
                        echo "    └── 内存条1: 型号=-, 容量=$physical_mem, 类型=-"
                    else
                        echo "    └── 无内存条信息"
                    fi
                fi
            else
                # 没有dmidecode数据，使用基本信息
                if [[ -n "$physical_mem" && "$physical_mem" != "-" ]]; then
                    echo "    └── 内存条1: 型号=-, 容量=$physical_mem, 类型=-"
                else
                    echo "    └── 无内存条信息"
                fi
            fi
        fi
        log_info "内存信息检测成功"
        return 0
    else
        echo "内存信息获取失败"
        log_error "内存信息检测失败"
        return 1
    fi
}

# 存储信息检测
get_storage_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "存储信息检测"
    echo "=========================================="

    # 声明变量
    local storage_info=""
    local storage_status=1

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始存储信息检测..."

        # 直接执行本地命令 - 使用与调试脚本完全相同的命令
        storage_info=$(lsblk 2>/dev/null && echo -e "\n=== df -h ===" && df -h 2>/dev/null && echo -e "\n=== fdisk -l ===" && fdisk -l 2>/dev/null)
        storage_status=$?  # 修复：保存命令退出状态

        # 添加调试信息
        echo "DEBUG: 本地存储检测 - 退出状态: $storage_status, 信息长度: ${#storage_info}" >&2
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始存储信息检测..."

        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show ver命令
            storage_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show ver" 2>/dev/null)
            storage_status=$?  # 修复：保存命令退出状态
        else
            # Linux系统 - 使用与原始数据收集相同的命令
            storage_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "lsblk 2>/dev/null && echo -e '\n=== df -h ===' && df -h 2>/dev/null && echo -e '\n=== fdisk -l ===' && fdisk -l 2>/dev/null" 2>/dev/null)
            storage_status=$?  # 修复：保存命令退出状态
        fi

        # 添加调试信息
        echo "DEBUG: 远程存储检测 - 退出状态: $storage_status, 信息长度: ${#storage_info}" >&2
    fi

    echo "DEBUG: 存储条件判断 - storage_status=$storage_status, storage_info非空=$([ -n "$storage_info" ] && echo "是" || echo "否")" >&2
    if [[ $storage_status -eq 0 && -n "$storage_info" ]]; then
        echo "存储信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show ver输出中尝试提取存储信息
            local storage_data=$(echo "$storage_info" | grep -v "INFO\|ERROR\|WARN")

            # 尝试从输出中查找存储相关信息（通常AFW3000的show ver不包含详细存储信息）
            local total_storage=$(echo "$storage_data" | grep -i "storage\|disk\|flash\|存储" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local flash_info=$(echo "$storage_data" | grep -i "flash" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')
            local disk_info=$(echo "$storage_data" | grep -i "disk\|ssd\|hdd" | head -1 | cut -d':' -f2 | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            echo "  └── 总容量：${total_storage:-"-"}"
            echo "  硬盘列表:"
            local storage_items=()
            if [[ -n "$flash_info" && "$flash_info" != "-" ]]; then
                storage_items+=("Flash存储: $flash_info")
            fi
            if [[ -n "$disk_info" && "$disk_info" != "-" ]]; then
                storage_items+=("磁盘存储: $disk_info")
            fi

            if [[ ${#storage_items[@]} -gt 0 ]]; then
                for i in "${!storage_items[@]}"; do
                    if [[ $i -eq $((${#storage_items[@]} - 1)) ]]; then
                        echo "    └── ${storage_items[i]}"
                    else
                        echo "    ├── ${storage_items[i]}"
                    fi
                done
            else
                echo "    └── 无存储设备信息"
            fi
        else
            # Linux系统从实际输出中提取存储信息
            local storage_data=$(echo "$storage_info" | grep -v "INFO\|ERROR\|WARN")

            # 从df命令输出中提取根分区信息（可用容量）
            local available_storage=$(echo "$storage_data" | grep "/$" | awk '{print $2}' | head -1)
            local root_device=$(echo "$storage_data" | grep "/$" | awk '{print $1}' | head -1)

            # 从lsblk输出中提取物理硬盘总容量
            local physical_storage=""
            local lsblk_data=$(echo "$storage_data" | grep -E "^[a-z]+[[:space:]]+[0-9]+:[0-9]+[[:space:]]+[0-9]+[[:space:]]+[0-9.]+[KMGT]")
            if [[ -n "$lsblk_data" ]]; then
                # 提取主要硬盘的容量（通常是sda或nvme0n1）
                local main_disk_size=$(echo "$lsblk_data" | grep -E "^(sda|nvme0n1|mmcblk0)" | head -1 | awk '{print $4}')
                if [[ -n "$main_disk_size" ]]; then
                    physical_storage="$main_disk_size"
                else
                    physical_storage="${available_storage:-"-"}"
                fi
            else
                physical_storage="${available_storage:-"-"}"
            fi

            # 计算存储总容量（所有物理硬盘容量的总和）
            local total_storage_capacity=""
            local lsblk_data=$(echo "$storage_data" | grep -E "^[a-z]+[[:space:]]+[0-9]+:[0-9]+[[:space:]]+[0-9]+[[:space:]]+[0-9.]+[KMGT]")
            if [[ -n "$lsblk_data" ]]; then
                # 提取所有物理硬盘的容量（排除分区和虚拟设备）
                local disk_sizes=($(echo "$lsblk_data" | grep -E "^(sd[a-z]|nvme[0-9]+n[0-9]+|mmcblk[0-9]+)[[:space:]]" | awk '{print $4}'))
                if [[ ${#disk_sizes[@]} -gt 0 ]]; then
                    local total_gb=0
                    for size in "${disk_sizes[@]}"; do
                        local value=$(echo "$size" | grep -o '[0-9.]*' | cut -d'.' -f1)
                        local size_unit=$(echo "$size" | grep -o '[KMGT]')
                        case "$size_unit" in
                        "T") total_gb=$((total_gb + value * 1024)) ;;
                        "G") total_gb=$((total_gb + value)) ;;
                        "M") total_gb=$((total_gb + value / 1024)) ;;
                        "K") total_gb=$((total_gb + value / 1024 / 1024)) ;;
                        esac
                    done

                    # 格式化显示
                    if [[ $total_gb -gt 1024 ]]; then
                        local total_tb=$((total_gb / 1024))
                        total_storage_capacity="${total_tb}TB"
                    else
                        total_storage_capacity="${total_gb}GB"
                    fi
                else
                    total_storage_capacity="${available_storage:-"-"}"
                fi
            else
                total_storage_capacity="${available_storage:-"-"}"
            fi

            echo "  └── 总容量：${total_storage_capacity:-"-"}"
            echo "  硬盘列表:"

            # 尝试从hwinfo和fdisk输出中提取详细硬盘信息
            local hwinfo_data=$(echo "$storage_data" | sed -n '/=== Hardware Disk Info ===/,/=== Disk Partitions ===/p')
            local fdisk_data=$(echo "$storage_data" | sed -n '/=== Disk Partitions ===/,$p')

            local disk_count=1
            local found_disks=false
            local disk_list=()

            # 首先尝试从fdisk -l输出中提取磁盘信息
            if [[ -n "$fdisk_data" ]]; then
                local disk_lines=$(echo "$fdisk_data" | grep "^Disk /dev/" | grep -v "/dev/ram" | grep -v "/dev/mmcblk[0-9]*boot" | grep -v "/dev/loop" | grep -v "===" | head -5)

                while IFS= read -r line; do
                    if [[ -n "$line" ]]; then
                        # 解析fdisk输出: Disk /dev/sda: 20 GiB, 21474836480 bytes, 41943040 sectors
                        if [[ "$line" =~ Disk[[:space:]]+(/dev/[^:]+):[[:space:]]*([^,]+) ]]; then
                            local disk_device="${BASH_REMATCH[1]}"
                            local disk_size="${BASH_REMATCH[2]}"

                            # 验证设备名称和容量格式是否有效
                            if [[ ! "$disk_device" =~ ^/dev/[a-zA-Z0-9]+$ ]] || [[ "$disk_size" =~ ^[=]+$ ]] || [[ "$disk_device" =~ === ]] || [[ "$disk_size" =~ === ]]; then
                                continue  # 跳过无效的条目
                            fi

                            # 尝试从fdisk输出中获取磁盘型号信息
                            local disk_model=""
                            local disk_type=""

                            # 查找Disk model行 - 修复：扩大搜索范围并改进匹配
                            local model_line=$(echo "$fdisk_data" | grep -A 5 "^Disk $disk_device:" | grep "Disk model:" | head -1)
                            if [[ -n "$model_line" ]]; then
                                disk_model=$(echo "$model_line" | sed 's/Disk model:[[:space:]]*//' | sed 's/[[:space:]]*$//')
                            else
                                # 如果没有找到Disk model行，尝试从其他位置提取型号信息
                                local device_section=$(echo "$fdisk_data" | grep -A 10 "^Disk $disk_device:")
                                if echo "$device_section" | grep -q "Model:"; then
                                    disk_model=$(echo "$device_section" | grep "Model:" | head -1 | sed 's/.*Model:[[:space:]]*//' | sed 's/[[:space:]]*$//')
                                fi
                            fi

                            # 判断磁盘类型（基于设备名称和型号） - 修复：完善类型识别逻辑
                            if [[ "$disk_device" =~ mmcblk[0-9]+$ ]]; then
                                disk_type="eMMC"
                            elif [[ "$disk_device" =~ nvme ]]; then
                                disk_type="NVMe SSD"
                            elif [[ "$disk_model" =~ SSD|ssd|YSIS|Samsung.*SSD|Intel.*SSD ]]; then
                                disk_type="SSD"
                            elif [[ "$disk_model" =~ HDD|hdd|WD|Seagate|Toshiba.*HDD ]]; then
                                disk_type="HDD"
                            else
                                # 尝试从hwinfo中获取类型信息（如果可用）
                                if [[ -n "$hwinfo_data" && ! "$hwinfo_data" =~ "hwinfo command not available" ]]; then
                                    local device_section=$(echo "$hwinfo_data" | grep -A 20 "$disk_device" | head -20)
                                    if echo "$device_section" | grep -qi "ssd\|solid"; then
                                        disk_type="SSD"
                                    elif echo "$device_section" | grep -qi "hdd\|hard"; then
                                        disk_type="HDD"
                                    else
                                        disk_type="Unknown"
                                    fi
                                else
                                    # 基于设备名称的最后判断
                                    if [[ "$disk_device" =~ ^/dev/sd[a-z]$ ]]; then
                                        disk_type="SATA"
                                    else
                                        disk_type="Unknown"
                                    fi
                                fi
                            fi

                            disk_list+=("硬盘${disk_count}: 设备=$disk_device, 容量=$disk_size, 类型=${disk_type:-"-"}, 型号=${disk_model:-"-"}")
                            disk_count=$((disk_count + 1))
                            found_disks=true
                        fi
                    fi
                done <<<"$disk_lines"
            fi

            # 如果fdisk没有找到磁盘信息，尝试从lsblk输出中提取
            if [[ "$found_disks" == false ]]; then
                # 修复：过滤掉启动分区和其他不需要的设备
                local lsblk_disks=$(echo "$storage_data" | grep -E "disk" | grep -v "boot" | grep -v "ram" | grep -v "loop" | head -3)

                while IFS= read -r line; do
                    if [[ -n "$line" ]]; then
                        local disk_name=$(echo "$line" | awk '{print $1}')
                        local disk_size=$(echo "$line" | awk '{print $4}')

                        # 修复：额外验证设备名称，确保不包含启动分区
                        if [[ -n "$disk_name" && -n "$disk_size" && ! "$disk_name" =~ boot ]]; then
                            disk_list+=("硬盘${disk_count}: 设备=/dev/$disk_name, 容量=$disk_size, 类型=-, 型号=-")
                            disk_count=$((disk_count + 1))
                            found_disks=true
                        fi
                    fi
                done <<<"$lsblk_disks"
            fi

            # 输出硬盘列表，使用正确的树状结构
            if [[ ${#disk_list[@]} -gt 0 ]]; then
                local valid_disks=()
                # 过滤掉无效的硬盘条目
                for disk_entry in "${disk_list[@]}"; do
                    if [[ ! "$disk_entry" =~ === ]]; then
                        valid_disks+=("$disk_entry")
                    fi
                done

                # 输出有效的硬盘列表
                for i in "${!valid_disks[@]}"; do
                    if [[ $i -eq $((${#valid_disks[@]} - 1)) ]]; then
                        echo "    └── ${valid_disks[i]}"
                    else
                        echo "    ├── ${valid_disks[i]}"
                    fi
                done
            else
                # 如果仍然没有找到磁盘信息，显示基本信息
                if [[ -n "$root_device" && "$root_device" != "-" ]]; then
                    echo "    └── 硬盘1: 设备=$root_device, 容量=${available_storage:-"-"}, 类型=-, 型号=-"
                else
                    echo "    └── 无硬盘信息"
                fi
            fi
        fi
        log_info "存储信息检测成功"
        return 0
    else
        echo "存储信息获取失败"
        log_error "存储信息检测失败"
        return 1
    fi
}

# 网口信息检测
get_network_interface_info() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"
    local ssh_port="${5:-22}"

    echo ""
    echo "=========================================="
    echo "网口信息检测"
    echo "=========================================="

    # 声明变量
    local nic_info=""
    local nic_status=1

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "目标设备: localhost (本地主机)"
        echo ""

        log_info "开始网口信息检测..."

        # 直接执行本地命令 - 使用与调试脚本完全相同的命令，并添加ethtool信息
        nic_info=$(ip addr 2>/dev/null && echo -e "\n=== ifconfig ===" && ifconfig 2>/dev/null && echo -e "\n=== ip route ===" && ip route 2>/dev/null && echo -e "\n=== netstat -tuln ===" && netstat -tuln 2>/dev/null && echo -e "\n=== ss -tuln ===" && ss -tuln 2>/dev/null)
        nic_status=$?  # 修复：保存命令退出状态

        # 添加ethtool信息收集
        local ethtool_info=""
        # 检查ethtool命令是否可用
        if command -v ethtool >/dev/null 2>&1; then
            local interfaces=$(ip link show 2>/dev/null | grep -E '^[0-9]+:' | awk -F': ' '{print $2}' | grep -v -E '^(lo|sit[0-9]*|ip6tnl[0-9]*|virbr[0-9]*|docker[0-9]*|veth[0-9]*|tunl[0-9]*|gre[0-9]*|gretap[0-9]*|erspan[0-9]*|ip_vti[0-9]*|ip6_vti[0-9]*|ip6gre[0-9]*|br-[0-9a-f]*|tun[0-9]*|tap[0-9]*)(@.*)?$')
            if [[ -n "$interfaces" ]]; then
                ethtool_info="\n=== ethtool ==="
                while IFS= read -r interface; do
                    if [[ -n "$interface" ]]; then
                        ethtool_info+="\n### $interface:"
                        # 处理带@符号的接口名称，ethtool需要使用@符号前的部分
                        local ethtool_interface="${interface%%@*}"
                        # 尝试执行ethtool，如果失败则记录错误信息
                        local ethtool_output=$(ethtool "$ethtool_interface" 2>/dev/null)
                        if [[ $? -eq 0 && -n "$ethtool_output" ]]; then
                            ethtool_info+="\n$ethtool_output"
                        else
                            ethtool_info+="\nethtool $ethtool_interface 命令执行失败或无权限"
                        fi
                    fi
                done <<<"$interfaces"
            else
                ethtool_info="\n=== ethtool ===\n未找到可检测的物理网络接口"
            fi
        else
            ethtool_info="\n=== ethtool ===\nethtool命令不可用，请安装ethtool工具"
        fi
        nic_info="$nic_info$ethtool_info"

        # 添加调试信息
        echo "DEBUG: 本地网口检测 - 退出状态: $nic_status, 信息长度: ${#nic_info}" >&2
    else
        # 远程检测模式（保持原有逻辑）
        echo "目标设备: $device_name ($device_ip)"
        echo ""

        log_info "开始网口信息检测..."

        if [[ "$device_name" == "n1" ]]; then
            # AFW3000防火墙设备使用show interface命令
            nic_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show interface" 2>/dev/null)
            nic_status=$?  # 修复：保存命令退出状态
        else
            # Linux系统 - 使用与原始数据收集相同的命令，并添加ethtool信息
            nic_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "ip addr 2>/dev/null && echo -e '\n=== ifconfig ===' && ifconfig 2>/dev/null && echo -e '\n=== ip route ===' && ip route 2>/dev/null && echo -e '\n=== netstat -tuln ===' && netstat -tuln 2>/dev/null && echo -e '\n=== ss -tuln ===' && ss -tuln 2>/dev/null" 2>/dev/null)
            nic_status=$?  # 修复：保存命令退出状态

            # 添加ethtool信息收集
            if [[ $nic_status -eq 0 ]]; then
                # 构建远程ethtool检测命令，包含错误处理和接口名称处理
                local remote_ethtool_cmd="echo -e '\n=== ethtool ==='; if command -v ethtool >/dev/null 2>&1; then interfaces=\$(ip link show 2>/dev/null | grep -E '^[0-9]+:' | awk -F': ' '{print \$2}' | grep -v -E '^(lo|sit[0-9]*|ip6tnl[0-9]*|virbr[0-9]*|docker[0-9]*|veth[0-9]*|tunl[0-9]*|gre[0-9]*|gretap[0-9]*|erspan[0-9]*|ip_vti[0-9]*|ip6_vti[0-9]*|ip6gre[0-9]*|br-[0-9a-f]*|tun[0-9]*|tap[0-9]*)(@.*)?$'); if [[ -n \"\$interfaces\" ]]; then while IFS= read -r interface; do if [[ -n \"\$interface\" ]]; then echo \"### \$interface:\"; ethtool_interface=\${interface%%@*}; ethtool_output=\$(ethtool \"\$ethtool_interface\" 2>/dev/null); if [[ \$? -eq 0 && -n \"\$ethtool_output\" ]]; then echo \"\$ethtool_output\"; else echo \"ethtool \$ethtool_interface 命令执行失败或无权限\"; fi; fi; done <<<\"\$interfaces\"; else echo \"未找到可检测的物理网络接口\"; fi; else echo \"ethtool命令不可用，请安装ethtool工具\"; fi"

                local ethtool_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "$remote_ethtool_cmd" 2>/dev/null | grep -v "INFO\|ERROR\|WARN")
                if [[ -n "$ethtool_info" ]]; then
                    nic_info="$nic_info"$'\n'"$ethtool_info"
                fi
            fi
        fi

        # 添加调试信息
        echo "DEBUG: 远程网口检测 - 退出状态: $nic_status, 信息长度: ${#nic_info}" >&2
    fi

    echo "DEBUG: 网口条件判断 - nic_status=$nic_status, nic_info非空=$([ -n "$nic_info" ] && echo "是" || echo "否")" >&2
    if [[ $nic_status -eq 0 && -n "$nic_info" ]]; then
        echo "网口信息获取成功"
        # 显示关键信息
        if [[ "$device_name" == "n1" ]]; then
            # 从show interface输出中动态提取网口信息
            local interface_data=$(echo "$nic_info" | grep -v "INFO\|ERROR\|WARN" | grep -E "ge[0-9]|bvi[0-9]")
            local interface_count=$(echo "$interface_data" | grep "ge[0-9]" | wc -l)

            echo "  网口总数: ${interface_count}个千兆网口"
            echo "  网口列表:"

            # 解析每个网口的信息，按照设计文档格式显示
            local configured_interfaces=()
            local unconfigured_interfaces=()

            # 使用数组方式处理接口数据，避免while循环的问题
            IFS=$'\n' read -d '' -r -a interface_lines <<<"$interface_data"

            for line in "${interface_lines[@]}"; do
                if [[ "$line" =~ ge[0-9] ]]; then
                    local interface_name=$(echo "$line" | awk '{print $1}')
                    local ip_addr=$(echo "$line" | awk '{print $2}')
                    local mac_addr=$(echo "$line" | awk '{print $5}')
                    local link_state=$(echo "$line" | awk '{print $4}')

                    # 动态获取接口速率信息
                    local interface_speed="-"
                    local detailed_info=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port" "show interface $interface_name" 2>/dev/null)
                    if [[ $? -eq 0 && -n "$detailed_info" ]]; then
                        # 从详细输出中提取速率信息
                        local speed_line=$(echo "$detailed_info" | grep -E "Speed:" | head -1)
                        if [[ -n "$speed_line" ]]; then
                            local speed_value=$(echo "$speed_line" | sed 's/.*Speed:[[:space:]]*\([0-9]*\).*/\1/')
                            if [[ -n "$speed_value" && "$speed_value" =~ ^[0-9]+$ ]]; then
                                interface_speed="${speed_value}Mbps"
                            fi
                        fi
                    fi

                    if [[ "$ip_addr" == "-" ]]; then
                        unconfigured_interfaces+=("$interface_name")
                    else
                        configured_interfaces+=("$interface_name: IP=$ip_addr, MAC=$mac_addr, 速率=$interface_speed")
                    fi
                fi
            done

            # 显示已配置的网口
            for i in "${!configured_interfaces[@]}"; do
                if [[ $i -eq $((${#configured_interfaces[@]} - 1)) && ${#unconfigured_interfaces[@]} -eq 0 ]]; then
                    # 如果是最后一个且没有未配置的网口，使用└──
                    echo "    └── ${configured_interfaces[i]}"
                else
                    echo "    ├── ${configured_interfaces[i]}"
                fi
            done

            # 显示未配置的网口
            if [[ ${#unconfigured_interfaces[@]} -gt 0 ]]; then
                local unconfigured_list=""
                for interface in "${unconfigured_interfaces[@]}"; do
                    if [[ -z "$unconfigured_list" ]]; then
                        unconfigured_list="$interface"
                    else
                        unconfigured_list="$unconfigured_list, $interface"
                    fi
                done
                echo "    └── $unconfigured_list: 未配置"
            fi
        else
            # Linux系统从实际输出中提取网口信息
            local nic_data=$(echo "$nic_info" | grep -v "INFO\|ERROR\|WARN")

            # 从ip addr show输出中提取网口信息，先过滤虚拟接口再统计
            local all_interfaces=$(echo "$nic_data" | grep -E "^[0-9]+:" | awk '{print $2}' | sed 's/:$//')

            # 过滤掉虚拟接口和回环接口，只保留物理网络接口
            local interfaces=""
            while IFS= read -r interface; do
                if [[ -n "$interface" ]]; then
                    # 过滤规则：排除回环接口、虚拟隧道接口、Docker接口等
                    if [[ ! "$interface" =~ ^(lo|sit[0-9]*@.*|ip6tnl[0-9]*@.*|tunl[0-9]*@.*|gre[0-9]*@.*|gretap[0-9]*@.*|erspan[0-9]*@.*|ip_vti[0-9]*@.*|ip6_vti[0-9]*@.*|ip6gre[0-9]*@.*|docker[0-9]*|virbr[0-9]*|veth.*|br-.*|tun[0-9]*|tap[0-9]*)$ ]]; then
                        # 只保留物理网络接口：eth, LAN, WAN, ens, enp等（包括带@的容器网络接口）
                        if [[ "$interface" =~ ^(eth[0-9]+(@.*)?|LAN[0-9]*|WAN[0-9]*|ens[0-9]+|enp[0-9]+s[0-9]+|em[0-9]+)$ ]]; then
                            if [[ -z "$interfaces" ]]; then
                                interfaces="$interface"
                            else
                                interfaces="$interfaces"$'\n'"$interface"
                            fi
                        fi
                    fi
                fi
            done <<<"$all_interfaces"

            local interface_count=$(echo "$interfaces" | grep -v '^$' | wc -l)

            echo "  网口总数: ${interface_count}个接口"
            echo "  网口列表:"

            # 解析每个网口的信息
            local interface_lines=()
            if [[ -n "$interfaces" && "$interface_count" -gt 0 ]]; then
                while IFS= read -r interface; do
                    if [[ -n "$interface" ]]; then
                        # 提取IP地址 - 修复：使用精确匹配避免接口间数据交叉污染
                        local ip_addr=""

                        # 查找该接口的完整信息块，使用精确的边界匹配
                        # 获取当前接口的行号
                        local current_interface_line=$(echo "$nic_data" | grep -n "^[0-9]*: $interface:" | cut -d: -f1)

                        if [[ -n "$current_interface_line" ]]; then
                            # 查找下一个接口的行号
                            local next_interface_line=$(echo "$nic_data" | grep -n "^[0-9]*: " | awk -F: -v current="$current_interface_line" '$1 > current {print $1; exit}')

                            if [[ -n "$next_interface_line" ]]; then
                                # 有下一个接口，精确匹配到下一个接口前
                                interface_block=$(echo "$nic_data" | sed -n "${current_interface_line},$((next_interface_line-1))p")
                            else
                                # 最后一个接口，查找第一个命令分隔符
                                local first_separator_line=$(echo "$nic_data" | grep -n "^===" | head -1 | cut -d: -f1)

                                if [[ -n "$first_separator_line" && "$first_separator_line" -gt "$current_interface_line" ]]; then
                                    # 匹配到第一个分隔符前
                                    interface_block=$(echo "$nic_data" | sed -n "${current_interface_line},$((first_separator_line-1))p")
                                else
                                    # 备用方案：只取当前接口行和紧随的几行，直到遇到空行或其他分隔符
                                    interface_block=$(echo "$nic_data" | sed -n "${current_interface_line},/^$/p" | sed '/^$/d')
                                    if [[ -z "$interface_block" ]]; then
                                        # 如果没有空行分隔，取固定行数
                                        interface_block=$(echo "$nic_data" | sed -n "${current_interface_line},$((current_interface_line+5))p")
                                    fi
                                fi
                            fi
                        else
                            interface_block=""
                        fi

                        # 从该接口块中提取IP地址
                        if [[ -n "$interface_block" ]]; then
                            local inet_lines=$(echo "$interface_block" | grep "inet ")
                            local filtered_inet=$(echo "$inet_lines" | grep -v "127.0.0.1")
                            ip_addr=$(echo "$filtered_inet" | awk '{print $2}' | head -1)
                        fi
                        # 提取MAC地址 - 修复：使用精确匹配确保与IP提取逻辑一致
                        local mac_addr=$(echo "$nic_data" | grep -A 10 "^[0-9]*: $interface:" | grep "link/ether" | awk '{print $2}' | head -1)

                        # 提取网口速率信息 - 优先显示硬件配置能力，其次显示当前链路速率
                        local interface_speed="-"
                        if [[ -n "$nic_info" ]]; then
                            # 查找该接口的ethtool输出
                            local ethtool_section=$(echo "$nic_info" | sed -n "/### $interface:/,/### /p" | sed '$d')
                            if [[ -z "$ethtool_section" ]]; then
                                # 如果没有找到下一个###，则取到文件末尾
                                ethtool_section=$(echo "$nic_info" | sed -n "/### $interface:/,\$p")
                            fi

                            if [[ -n "$ethtool_section" ]]; then
                                # 首先尝试获取当前链路速率
                                local current_speed_line=$(echo "$ethtool_section" | grep -E "^[[:space:]]*Speed:" | head -1)
                                if [[ -n "$current_speed_line" ]]; then
                                    # 检查是否包含有效的数字速率
                                    if echo "$current_speed_line" | grep -qE "Speed:[[:space:]]*[0-9]+[[:space:]]*[MGT]?b/s"; then
                                        local current_speed=$(echo "$current_speed_line" | sed -E 's/^[[:space:]]*Speed:[[:space:]]*([0-9]+[[:space:]]*[MGT]?b\/s).*/\1/' | sed 's/[[:space:]]//g')
                                        interface_speed="$current_speed"
                                    fi
                                fi

                                # 如果当前速率无效（Unknown或down状态），尝试获取硬件最大支持速率
                                if [[ "$interface_speed" == "-" ]]; then
                                    # 从Supported link modes中提取最高支持速率
                                    local supported_modes=$(echo "$ethtool_section" | grep -A 10 "Supported link modes:" | grep -E "[0-9]+[MGT]?baseT")
                                    if [[ -n "$supported_modes" ]]; then
                                        # 提取最高速率：优先级 10000 > 1000 > 100 > 10
                                        if echo "$supported_modes" | grep -q "10000baseT"; then
                                            interface_speed="10000Mb/s"
                                        elif echo "$supported_modes" | grep -q "1000baseT"; then
                                            interface_speed="1000Mb/s"
                                        elif echo "$supported_modes" | grep -q "100baseT"; then
                                            interface_speed="100Mb/s"
                                        elif echo "$supported_modes" | grep -q "10baseT"; then
                                            interface_speed="10Mb/s"
                                        fi
                                    fi
                                fi
                            fi
                        fi

                        if [[ -n "$ip_addr" ]]; then
                            interface_lines+=("$interface: IP=$ip_addr, MAC=${mac_addr:-"-"}, 速率=$interface_speed")
                        else
                            interface_lines+=("$interface: 未配置IP, MAC=${mac_addr:-"-"}, 速率=$interface_speed")
                        fi
                    fi
                done <<<"$interfaces"
            fi

            # 显示网口信息，使用正确的树状结构
            for i in "${!interface_lines[@]}"; do
                if [[ $i -eq $((${#interface_lines[@]} - 1)) ]]; then
                    echo "    └── ${interface_lines[i]}"
                else
                    echo "    ├── ${interface_lines[i]}"
                fi
            done
        fi
        log_info "网口信息检测成功"
        return 0
    else
        echo "网口信息获取失败"
        log_error "网口信息检测失败"
        return 1
    fi
}

# =============================================================================
# 主检测流程
# =============================================================================

run_hardware_check() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"

    # 从配置中获取SSH端口
    local ssh_port="22"
    if [[ -f "config/devices.conf" ]]; then
        source "config/devices.conf"
        local port_var="${device_name^^}_PORT"
        ssh_port="${!port_var:-22}"
    fi

    echo "=========================================="
    echo "$MODULE_NAME v$MODULE_VERSION"
    echo "=========================================="
    echo "检测时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "目标设备: $device_name"
    echo "设备地址: $device_ip:$ssh_port"
    echo ""

    local overall_success=true
    local success_count=0
    local total_count=5

    # 1. 主板信息检测
    if get_motherboard_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 2. CPU信息检测
    if get_cpu_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 3. 内存信息检测
    if get_memory_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 4. 存储信息检测
    if get_storage_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    # 5. 网口信息检测
    if get_network_interface_info "$device_name" "$device_ip" "$ssh_user" "$ssh_password" "$ssh_port"; then
        success_count=$((success_count + 1))
    else
        overall_success=false
    fi

    echo ""
    echo "=========================================="
    echo "硬件检测完成"
    echo "=========================================="
    echo "检测项目: $total_count"
    echo "成功项目: $success_count"
    echo "失败项目: $((total_count - success_count))"

    if [[ "$overall_success" == true ]]; then
        echo "检测结果: 硬件信息获取完整"
        log_info "硬件检测全部通过"
        return 0
    else
        echo "检测结果: ⚠ 硬件信息获取不完整"
        log_warn "硬件检测存在问题"
        return 1
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 验证参数
    if ! validate_parameters "$@"; then
        exit 1
    fi

    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"

    # 执行硬件检测
    if run_hardware_check "$device_name" "$device_ip" "$ssh_user" "$ssh_password"; then
        exit 0
    else
        exit 1
    fi
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 设置错误处理
set -e
trap 'log_error "硬件检测模块异常退出"; exit 1' ERR

# 调用主函数
main "$@"
