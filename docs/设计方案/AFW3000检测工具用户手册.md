# AFW3000 防火墙检测工具用户手册

## 目录

- [1. 项目概述](#1-项目概述)
- [2. 架构说明](#2-架构说明)
- [3. 安装部署](#3-安装部署)
- [4. 使用指南](#4-使用指南)
- [5. 检测内容说明](#5-检测内容说明)
- [6. 报告解读](#6-报告解读)
- [7. 故障排查](#7-故障排查)
- [8. 扩展开发](#8-扩展开发)

---

## 1. 项目概述

### 1.1 功能定位

AFW3000 防火墙检测工具是一个专业的网络设备和 Linux 系统检测工具，主要用于：

- **AFW3000 防火墙设备**的远程检测和状态监控
- **Linux 系统节点**的远程和本地检测
- **多节点批量检测**和统一报告生成
- **系统运维**和**故障排查**支持

### 1.2 适用场景

- **企业网络设备巡检**：定期检测防火墙和服务器状态
- **系统健康监控**：实时了解设备运行状况
- **故障排查支持**：快速收集设备配置和状态信息
- **合规性检查**：生成标准化的检测报告
- **运维自动化**：集成到自动化运维流程中

### 1.3 版本信息

- **当前版本**：1.0
- **开发者**：dkyzheng
- **支持平台**：Linux 系统
- **支持设备**：AFW3000 防火墙、Linux 服务器

### 1.4 核心特性

- ✅ **双模式检测**：支持远程 SSH 检测和本地直接检测
- ✅ **多设备支持**：AFW3000 防火墙 + Linux 系统
- ✅ **模块化架构**：网络、硬件、系统、软件独立检测
- ✅ **详细报告**：结构化报告 + 原始数据附录
- ✅ **跨平台兼容**：支持多种 Linux 发行版和系统架构
- ✅ **调试支持**：完整的日志记录和调试模式

---

## 2. 架构说明

### 2.1 项目目录结构

```
afw3000-check/
├── afw3000_check.sh          # 主入口脚本
├── config/                   # 配置文件目录
│   ├── devices.conf          # 设备连接配置
│   ├── check_config.conf     # 检测参数配置
│   └── software_check.conf   # 软件检测配置
├── lib/                      # 公共函数库
│   ├── common_functions.sh   # 通用工具函数
│   ├── ssh_utils.sh          # SSH连接工具
│   └── web_check_utils.sh    # Web检测工具
├── modules/                  # 检测模块
│   ├── network_check.sh      # 网络检测模块
│   ├── hardware_check.sh     # 硬件检测模块
│   ├── system_check.sh       # 系统检测模块
│   └── software_check.sh     # 软件检测模块
├── tools/                    # 跨平台工具
│   ├── aarch64/sshpass       # ARM64架构工具
│   └── x86_64/sshpass        # x86_64架构工具
├── scripts/                  # 辅助脚本
├── report/                   # 检测报告输出目录
└── docs/                     # 文档目录
```

### 2.2 调用链路图

```mermaid
graph TD
    A[afw3000_check.sh] --> B[参数解析]
    A --> C[环境初始化]
    A --> D[执行检测]
    A --> E[生成报告]

    C --> F[加载配置文件]
    C --> G[检查依赖工具]

    D --> H{检测模式判断}
    H -->|远程模式| I[远程检测分支]
    H -->|本地模式| J[本地检测分支]

    I --> K[SSH连接]
    I --> L[远程命令执行]

    J --> M[本地命令执行]

    L --> N[检测模块调用]
    M --> N

    N --> O[network_check.sh]
    N --> P[hardware_check.sh]
    N --> Q[system_check.sh]
    N --> R[software_check.sh]

    O --> S[临时结果文件]
    P --> S
    Q --> S
    R --> S

    S --> E
    E --> T[结构化报告]
```

### 2.3 检测模式对比

| 特性           | 远程检测模式              | 本地检测模式           |
| -------------- | ------------------------- | ---------------------- |
| **执行方式**   | SSH 远程连接              | 本地直接执行           |
| **适用场景**   | 集中管理、批量检测        | 单机检测、网络受限环境 |
| **网络要求**   | 需要 SSH 连接             | 无网络要求             |
| **权限要求**   | SSH 用户权限              | 本地执行权限           |
| **性能表现**   | 有网络延迟                | 无网络延迟，速度快     |
| **部署复杂度** | 需要配置 SSH 连接         | 直接运行即可           |
| **支持设备**   | n1(AFW3000)、n2/n3(Linux) | 仅支持 Linux 系统      |

---

## 3. 安装部署

### 3.1 环境要求

#### 系统要求

- **操作系统**：Linux (CentOS 7+, Ubuntu 16.04+, RHEL 7+)
- **架构支持**：x86_64, aarch64
- **Shell 环境**：Bash 4.0+

#### 软件依赖

```bash
# 基础工具（通常系统自带）
- bash
- grep, awk, sed
- cat, echo, date
- hostname, whoami

# 系统检测工具
- dmidecode      # 硬件信息检测
- lscpu          # CPU信息
- free           # 内存信息
- df, lsblk      # 存储信息
- ip, ethtool    # 网络信息

# 可选工具
- hwinfo         # 详细硬件信息（可选）
```

### 3.2 安装步骤

#### 方式一：直接部署

```bash
# 1. 下载工具包
wget https://example.com/afw3000-check.tar.gz
tar -xzf afw3000-check.tar.gz
cd afw3000-check

# 2. 设置执行权限
chmod +x afw3000_check.sh
chmod +x modules/*.sh
chmod +x tools/**/sshpass

# 3. 验证安装
./afw3000_check.sh --version
```

#### 方式二：Git 克隆

```bash
# 1. 克隆仓库
git clone https://github.com/example/afw3000-check.git
cd afw3000-check

# 2. 设置权限
chmod +x afw3000_check.sh modules/*.sh tools/**/sshpass

# 3. 验证安装
./afw3000_check.sh --help
```

### 3.3 配置文件设置

#### 3.3.1 设备连接配置 (config/devices.conf)

```bash
# N1节点配置（AFW3000防火墙）
N1_HOST="*************"
N1_PORT="36863"
N1_USER="admin"
N1_PASSWORD="admin@123"

# N2节点配置（Linux系统）
N2_HOST="**************"
N2_PORT="22"
N2_USER="root"
N2_PASSWORD="ydsoc_12345678"
N2_BACKUP_USER="ydsoc"
N2_BACKUP_PASSWORD="ydsoc_12345678"

# N3节点配置（Linux系统）
N3_HOST="*************"
N3_PORT="36863"
N3_USER="root"
N3_PASSWORD="ydsoc_12345678"
```

#### 3.3.2 检测参数配置 (config/check_config.conf)

```bash
# 网络检测参数
PING_COUNT=3
PING_TIMEOUT=5
CHECK_PORTS="80,163,443,36863"

# 系统状态阈值
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=80

# SSH连接参数
SSH_TIMEOUT=10
SSH_RETRY_COUNT=3
```

### 3.4 权限要求

#### 远程检测权限

- **AFW3000 设备**：管理员账户权限
- **Linux 系统**：root 权限或 sudo 权限
- **网络访问**：SSH 端口连通性

#### 本地检测权限

- **系统信息**：普通用户权限
- **硬件信息**：root 权限（dmidecode 等命令）
- **网络信息**：普通用户权限

### 3.5 安全注意事项

1. **密码安全**：

   - 配置文件权限设置为 600
   - 避免在命令行中暴露密码
   - 定期更换设备密码

2. **网络安全**：

   - 限制 SSH 访问来源 IP
   - 使用非标准 SSH 端口
   - 启用 SSH 密钥认证（推荐）

3. **文件权限**：

```bash
# 设置安全权限
chmod 600 config/devices.conf
chmod 755 afw3000_check.sh
chmod 755 modules/*.sh
```

---

## 4. 使用指南

### 4.1 命令行参数说明

#### 基本语法

```bash
./afw3000_check.sh [选项] [目标节点]
```

#### 默认行为

**无参数时自动执行本地检测**（检测当前主机）

#### 参数详解

| 参数            | 说明                    | 示例                            |
| --------------- | ----------------------- | ------------------------------- |
| `--help, -h`    | 显示帮助信息            | `./afw3000_check.sh --help`     |
| `--version, -v` | 显示版本信息            | `./afw3000_check.sh --version`  |
| `--debug`       | 启用调试模式            | `./afw3000_check.sh --debug`    |
| `--tag, -t TAG` | 添加自定义标识符到报告文件名 | `./afw3000_check.sh --tag production n1` |
| `all`           | 检测所有远程节点        | `./afw3000_check.sh all`        |
| `n1`            | 检测 N1 节点（AFW3000） | `./afw3000_check.sh n1`         |
| `n2`            | 检测 N2 节点（Linux）   | `./afw3000_check.sh n2`         |
| `n3`            | 检测 N3 节点（Linux）   | `./afw3000_check.sh n3`         |

#### 自定义标识符说明

`--tag` 参数允许为报告文件名添加自定义标识符，便于区分不同的检测任务或环境：

- **格式限制**：仅支持字母和数字，最长50个字符
- **文件命名**：标识符会添加到时间戳之后
  - 远程检测：`afw3000_check_YYYYMMDD_HHMMSS_标识符.txt`
  - 本地检测：`afw3000_local_check_YYYYMMDD_HHMMSS_标识符.txt`
- **向后兼容**：不提供标识符时保持原有命名格式

### 4.2 基本使用方法

#### 4.2.1 本地检测（默认行为）

```bash
# 本地检测（默认行为）
./afw3000_check.sh

# 本地检测 + 调试模式
./afw3000_check.sh --debug
```

#### 4.2.2 远程检测

```bash
# 检测AFW3000防火墙
./afw3000_check.sh n1

# 检测Linux服务器
./afw3000_check.sh n2
./afw3000_check.sh n3

# 检测所有节点
./afw3000_check.sh all
```

#### 4.2.3 使用自定义标识符

```bash
# 生产环境检测
./afw3000_check.sh --tag production n1
# 生成文件：afw3000_check_20250801_143000_production.txt

# 测试环境检测
./afw3000_check.sh -t test all
# 生成文件：afw3000_check_20250801_143000_test.txt

# 本地检测带标识符
./afw3000_check.sh --tag backup
# 生成文件：afw3000_local_check_20250801_143000_backup.txt

# 维护前检测
./afw3000_check.sh --debug --tag premaintenance n2
# 生成文件：afw3000_check_20250801_143000_premaintenance.txt

# 多种环境标识示例
./afw3000_check.sh --tag dev123 n1        # 开发环境
./afw3000_check.sh --tag staging n2       # 预发布环境
./afw3000_check.sh --tag prod2024 all     # 生产环境年度检测
```

#### 4.2.4 组合使用示例

```bash
# 调试模式 + 自定义标识符
./afw3000_check.sh --debug --tag troubleshoot n1

# 批量检测不同环境
./afw3000_check.sh --tag env1 n1
./afw3000_check.sh --tag env2 n2
./afw3000_check.sh --tag env3 n3
```

#### 4.2.3 调试模式

```bash
# 调试模式检测单个节点
./afw3000_check.sh --debug n1

# 调试模式检测所有节点
./afw3000_check.sh --debug all
```

### 4.3 本地检测详细说明

#### 4.3.1 默认行为

从 v1.1 版本开始，**本地检测已成为默认行为**：

```bash
# 本地检测（默认行为，无需参数）
./afw3000_check.sh

# 等同于之前版本的
# ./afw3000_check.sh --local（已废弃）
```

#### 4.3.2 本地调试检测

```bash
# 本地检测 + 调试模式
./afw3000_check.sh --debug
```

#### 4.3.3 本地检测特点

- **默认行为**：无参数时自动执行本地检测
- **无需网络连接**：直接在本机执行
- **无需 SSH 配置**：跳过远程连接设置
- **快速执行**：无网络延迟
- **仅支持 Linux**：不支持 AFW3000 防火墙本地检测

### 4.4 使用示例和预期输出

#### 示例 1：远程检测单个节点

```bash
$ ./afw3000_check.sh n2
[INFO] 2025-07-29 10:00:00 检测目标: n2节点
[INFO] 2025-07-29 10:00:01 开始检测 n2 节点...
[INFO] 2025-07-29 10:00:02 网络连通性检测成功
[INFO] 2025-07-29 10:00:05 硬件信息检测成功
[INFO] 2025-07-29 10:00:08 系统信息检测成功
[INFO] 2025-07-29 10:00:10 软件信息检测成功
[INFO] 2025-07-29 10:00:11 n2 节点检测成功
[INFO] 2025-07-29 10:00:11 检测报告已生成: report/afw3000_check_20250729_100000.txt
```

#### 示例 2：本地检测（默认行为）

```bash
$ ./afw3000_check.sh
[INFO] 2025-07-29 10:00:00 默认执行本地检测模式
[INFO] 2025-07-29 10:00:00 本地检测模式，跳过远程配置加载和SSH依赖检查
[INFO] 2025-07-29 10:00:01 开始本地检测...
[INFO] 2025-07-29 10:00:01 本地主机信息: server01 (*************)
[INFO] 2025-07-29 10:00:02 执行硬件检测...
[INFO] 2025-07-29 10:00:05 本地硬件检测成功
[INFO] 2025-07-29 10:00:05 执行系统信息检测...
[INFO] 2025-07-29 10:00:07 本地系统信息检测成功
[INFO] 2025-07-29 10:00:07 执行软件信息检测...
[INFO] 2025-07-29 10:00:09 本地软件信息检测成功
[INFO] 2025-07-29 10:00:09 检测报告已生成: report/afw3000_local_check_20250729_100000.txt
```

---

## 5. 检测内容说明

### 5.1 检测模块概览

AFW3000 检测工具包含四个核心检测模块：

| 模块         | 功能                  | 远程检测 | 本地检测  |
| ------------ | --------------------- | -------- | --------- |
| **网络检测** | SSH 连接、端口检测    | ✅       | ❌ (跳过) |
| **硬件检测** | CPU、内存、存储、网口 | ✅       | ✅        |
| **系统检测** | 版本、状态、负载      | ✅       | ✅        |
| **软件检测** | 服务状态、版本信息    | ✅       | ✅        |

### 5.2 网络检测模块 (network_check.sh)

#### 检测内容

- **SSH 连接测试**：验证设备可达性
- **端口连通性**：检测关键服务端口
- **网络延迟**：测量响应时间

#### 检测命令

```bash
# AFW3000设备
show interface

# Linux系统
ping -c 3 target_host
nc -zv target_host port
```

#### 本地检测说明

本地检测模式下跳过网络检测，因为无需验证 SSH 连接。

### 5.3 硬件检测模块 (hardware_check.sh)

#### 检测内容

##### 5.3.1 主板信息

- **远程检测**：`dmidecode -t baseboard`
- **本地检测**：直接执行 `dmidecode -t baseboard`
- **AFW3000**：`show ver` 命令获取平台信息

##### 5.3.2 CPU 信息

```bash
# 检测命令
cat /proc/cpuinfo | grep -E 'model name|cpu cores|processor|cpu MHz|BogoMIPS'

# 输出示例
model name: Intel(R) Xeon(R) CPU E5-2680 v4 @ 2.40GHz
cpu cores: 14
processor: 0-27
cpu MHz: 2394.454
```

##### 5.3.3 内存信息

```bash
# 检测命令
free -h && cat /proc/meminfo | head -5 && dmidecode -t memory

# 输出示例
              total        used        free      shared  buff/cache   available
Mem:           62G        8.2G         45G        1.2G        8.8G         52G
Swap:         4.0G          0B        4.0G
```

##### 5.3.4 存储信息

```bash
# 检测命令
df -h && lsblk && fdisk -l

# 输出示例
Filesystem      Size  Used Avail Use% Mounted on
/dev/sda1        50G   12G   36G  25% /
/dev/sda2       100G   45G   50G  48% /data
```

##### 5.3.5 网口信息

```bash
# 检测命令
ip addr show && ethtool eth0

# 输出示例
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP
    inet *************/24 brd ************* scope global eth0
```

### 5.4 系统检测模块 (system_check.sh)

#### 5.4.1 系统版本信息

```bash
# 检测命令
cat /etc/os-release && uname -a

# 输出示例
NAME="CentOS Linux"
VERSION="7 (Core)"
ID="centos"
VERSION_ID="7"
Linux server01 3.10.0-1160.el7.x86_64 #1 SMP Mon Oct 19 16:18:59 UTC 2020 x86_64 x86_64 x86_64 GNU/Linux
```

#### 5.4.2 系统状态监控

```bash
# 检测命令
uptime && cat /proc/loadavg && free -m && df -h /

# 输出示例
 10:30:45 up 15 days,  2:45,  3 users,  load average: 0.15, 0.25, 0.30
0.15 0.25 0.30 1/234 12345
              total        used        free      shared  buff/cache   available
Mem:          15936        2048       10240         256        3648       13376
Filesystem      Size  Used Avail Use% Mounted on
/dev/sda1        50G   12G   36G  25% /
```

### 5.5 软件检测模块 (software_check.sh)

#### 5.5.1 AFW3000 设备 (n1 节点)

- **Web 服务检测**：HTTP/HTTPS 服务状态
- **管理界面**：登录页面可访问性
- **响应时间**：服务响应性能

#### 5.5.2 Linux 系统 (n2/n3 节点)

基于 `config/software_check.conf` 配置文件检测：

```bash
# 配置示例
[nginx]
name=Nginx Web服务器
type=service
category=Web服务
check_cmd=systemctl is-active nginx
version_cmd=nginx -v 2>&1 | head -1
install_path=/etc/nginx
description=高性能Web服务器和反向代理

[mysql]
name=MySQL数据库
type=service
category=数据库
check_cmd=systemctl is-active mysqld
version_cmd=mysql --version | head -1
install_path=/var/lib/mysql
description=关系型数据库管理系统
```

#### 5.5.3 本地检测实现

本地检测直接执行配置文件中的 `check_cmd` 和 `version_cmd`：

```bash
# 服务状态检测
systemctl is-active nginx
systemctl is-active mysqld

# 版本信息获取
nginx -v
mysql --version
```

### 5.6 检测结果判断标准

#### 成功标准

- **网络检测**：SSH 连接成功，关键端口可达
- **硬件检测**：成功获取 CPU、内存、存储信息
- **系统检测**：系统版本识别，状态信息正常
- **软件检测**：配置的服务正常运行

#### 失败标准

- **网络检测**：SSH 连接失败，端口不可达
- **硬件检测**：命令执行失败，权限不足
- **系统检测**：系统信息获取失败
- **软件检测**：关键服务未运行

---

## 6. 报告解读

### 6.1 报告文件格式

#### 6.1.1 文件命名规则

```bash
# 远程检测报告
afw3000_check_YYYYMMDD_HHMMSS.txt

# 本地检测报告
afw3000_local_check_YYYYMMDD_HHMMSS.txt

# 示例
afw3000_check_20250729_143000.txt
afw3000_local_check_20250729_143000.txt
```

#### 6.1.2 报告存储位置

```bash
report/
├── afw3000_check_20250729_143000.txt
├── afw3000_local_check_20250729_143000.txt
└── ...
```

### 6.2 报告内容结构

#### 6.2.1 报告头部信息

```
========================================
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-29 14:30:00
检测范围：所有节点 (N1, N2, N3)
脚本版本：1.0
```

#### 6.2.2 检测概况统计

```
========================================
设备检测概况
========================================

总节点数：3
检测成功：2
检测失败：1
检测成功率：67%
```

#### 6.2.3 详细检测结果

```
========================================
设备详细检测结果
========================================

[N1节点 - *************]
├── 网络连通性：正常
│   ├── SSH连接：成功 (响应时间: 45ms)
│   ├── Web服务：正常 (HTTP: 200, HTTPS: 200)
│   └── 管理界面：可访问
├── 硬件信息：正常
│   ├── 平台：AFW3000-X1
│   ├── 型号：AFW3000
│   └── 产品：安博通防火墙
├── 系统信息：正常
│   ├── 主机名称：AFW3000-FW01
│   ├── 防火墙版本：V6.2.1
│   ├── 固件版本：6.2.1.20240315
│   └── 平台：AFW3000-X1
└── 软件信息：正常
    └── Web服务检测完成

[N2节点 - **************]
├── 网络连通性：正常
│   ├── SSH连接：成功 (响应时间: 23ms)
│   └── 端口检测：22/tcp 开放
├── 硬件信息：正常
│   ├── CPU型号：Intel(R) Xeon(R) CPU E5-2680 v4 @ 2.40GHz
│   ├── CPU核数：14核
│   ├── 内存容量：64GB
│   └── 存储容量：500GB
├── 系统信息：正常
│   ├── 主机名称：server02
│   ├── 操作系统：CentOS Linux 7 (Core)
│   ├── 内核版本：3.10.0-1160.el7.x86_64
│   ├── 系统架构：x86_64
│   ├── 系统负载：0.15, 0.25, 0.30
│   ├── 内存使用率：13%
│   ├── 磁盘使用率：25%
│   └── 运行时间：15 days, 2:45
└── 软件信息：正常
    ├── Nginx Web服务器：运行中 (v1.16.1)
    ├── MySQL数据库：运行中 (v8.0.25)
    └── Redis缓存：运行中 (v6.2.6)
```

#### 6.2.4 原始数据附录

```
================================================================================
原始命令输出附录
================================================================================

本附录包含获取各配置指标的原始命令执行结果，供技术分析和对照验证使用。

[N1节点原始命令输出]
========================================

硬件配置原始数据
--------------
Platform: AFW3000-X1
Model: AFW3000
Product: 安博通防火墙
...

系统信息原始数据
--------------
Hostname: AFW3000-FW01
Version: V6.2.1
Firmware: 6.2.1.20240315
...
```

### 6.3 检测结果解读方法

#### 6.3.1 状态标识说明

- **正常**：检测成功，设备状态良好
- **异常**：检测发现问题，需要关注
- **未检测**：检测失败，无法获取信息
- **不适用**：该检测项不适用于当前设备

#### 6.3.2 关键指标解读

##### CPU 使用率

- **正常**：< 80%
- **警告**：80% - 90%
- **异常**：> 90%

##### 内存使用率

- **正常**：< 85%
- **警告**：85% - 95%
- **异常**：> 95%

##### 磁盘使用率

- **正常**：< 80%
- **警告**：80% - 90%
- **异常**：> 90%

##### 系统负载 (Load Average)

- **正常**：< CPU 核数
- **警告**：CPU 核数 - CPU 核数\*1.5
- **异常**：> CPU 核数\*1.5

#### 6.3.3 常见问题识别

##### 网络问题

```
├── 网络连通性：异常
│   ├── SSH连接：失败 (连接超时)
│   └── 端口检测：22/tcp 关闭
```

**可能原因**：网络不通、防火墙阻断、SSH 服务未启动

##### 硬件问题

```
├── 硬件信息：异常
│   ├── CPU信息：获取失败
│   └── 内存信息：权限不足
```

**可能原因**：权限不足、硬件故障、系统异常

##### 系统问题

```
├── 系统信息：异常
│   ├── 系统负载：3.45, 3.20, 2.98 (CPU: 2核)
│   ├── 内存使用率：96%
│   └── 磁盘使用率：95%
```

**可能原因**：系统负载过高、资源不足、需要优化

##### 软件问题

```
└── 软件信息：异常
    ├── Nginx Web服务器：未运行
    ├── MySQL数据库：运行中 (v8.0.25)
    └── Redis缓存：未安装
```

**可能原因**：服务未启动、配置错误、软件未安装

---

## 7. 故障排查

### 7.1 常见错误及解决方案

#### 7.1.1 SSH 连接问题

##### 错误现象

```
[ERROR] SSH连接失败: Connection timed out
[ERROR] n2 节点检测失败
```

##### 可能原因

1. 网络不通
2. SSH 服务未启动
3. 防火墙阻断
4. 认证失败

##### 解决方案

```bash
# 1. 检查网络连通性
ping **************

# 2. 检查SSH端口
telnet ************** 22
nc -zv ************** 22

# 3. 检查SSH服务状态
systemctl status sshd

# 4. 检查防火墙规则
iptables -L | grep 22
firewall-cmd --list-ports

# 5. 手动SSH测试
ssh root@**************
```

#### 7.1.2 权限不足问题

##### 错误现象

```
[ERROR] 硬件信息检测失败
dmidecode: command not found or permission denied
```

##### 解决方案

```bash
# 1. 检查命令是否存在
which dmidecode

# 2. 安装缺失工具
# CentOS/RHEL
yum install dmidecode

# Ubuntu/Debian
apt-get install dmidecode

# 3. 检查权限
sudo dmidecode -t baseboard

# 4. 本地检测使用sudo
sudo ./afw3000_check.sh
```

#### 7.1.3 配置文件问题

##### 错误现象

```
[ERROR] 设备配置文件加载失败
[ERROR] 无效的IP地址格式: 192.168.1
```

##### 解决方案

```bash
# 1. 检查配置文件存在性
ls -la config/devices.conf

# 2. 检查配置文件格式
cat config/devices.conf

# 3. 验证IP地址格式
# 正确格式：*************
# 错误格式：192.168.1

# 4. 检查配置文件权限
chmod 600 config/devices.conf
```

#### 7.1.4 依赖工具问题

##### 错误现象

```
[ERROR] 依赖工具检查失败
[ERROR] sshpass command not found
```

##### 解决方案

```bash
# 1. 检查工具架构
uname -m

# 2. 验证工具存在
ls -la tools/x86_64/sshpass
ls -la tools/aarch64/sshpass

# 3. 设置执行权限
chmod +x tools/*/sshpass

# 4. 手动测试工具
./tools/x86_64/sshpass -V
```

### 7.2 日志分析方法

#### 7.2.1 启用调试模式

```bash
# 启用详细日志输出
./afw3000_check.sh --debug n2
./afw3000_check.sh --debug
```

#### 7.2.2 日志级别说明

- **[INFO]**：正常信息，流程进展
- **[WARN]**：警告信息，非致命问题
- **[ERROR]**：错误信息，需要处理

#### 7.2.3 关键日志分析

##### SSH 连接日志

```bash
[INFO] 开始SSH连接测试...
[INFO] SSH连接成功: **************:22
[INFO] SSH连接响应时间: 45ms
```

##### 命令执行日志

```bash
[INFO] 执行远程命令: cat /proc/cpuinfo
[INFO] 命令执行成功，输出长度: 2048字节
[ERROR] 命令执行失败，退出码: 1
```

##### 检测模块日志

```bash
[INFO] 开始硬件信息检测...
[INFO] CPU信息检测成功
[WARN] 内存信息检测失败
[INFO] 硬件信息检测完成
```

### 7.3 性能优化建议

#### 7.3.1 网络优化

```bash
# 1. 调整SSH超时时间
SSH_TIMEOUT=30

# 2. 减少重试次数
SSH_RETRY_COUNT=2

# 3. 并行检测（谨慎使用）
# 避免同时检测过多节点
```

#### 7.3.2 本地检测优化

```bash
# 1. 使用本地检测避免网络延迟（默认行为）
./afw3000_check.sh

# 2. 跳过不必要的检测
# 修改检测模块，注释不需要的检测项

# 3. 优化命令执行
# 使用更高效的命令组合
```

#### 7.3.3 系统资源优化

```bash
# 1. 监控系统资源使用
top -p $(pgrep -f afw3000_check)

# 2. 限制并发检测数量
# 避免同时运行多个检测实例

# 3. 清理临时文件
rm -f /tmp/afw3000_*
```

---

## 8. 扩展开发

### 8.1 添加新的检测模块

#### 8.1.1 创建检测模块文件

```bash
# 创建新模块文件
touch modules/custom_check.sh
chmod +x modules/custom_check.sh
```

#### 8.1.2 模块基本结构

```bash
#!/bin/bash
# 自定义检测模块
# 功能: 自定义检测功能
# 版本: 1.0

# =============================================================================
# 模块初始化
# =============================================================================

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$SCRIPT_DIR"

# 引入公共函数库
source "lib/common_functions.sh"
source "lib/ssh_utils.sh"

# =============================================================================
# 参数验证
# =============================================================================

validate_parameters() {
    # 本地检测模式：检查是否为localhost
    if [[ "$1" == "localhost" ]]; then
        return 0
    fi

    # 远程检测模式：参数验证
    if [[ $# -ne 4 ]]; then
        log_error "参数错误"
        echo "用法: $0 <设备名称> <设备IP> <SSH用户名> <SSH密码>"
        return 1
    fi

    return 0
}

# =============================================================================
# 检测功能实现
# =============================================================================

custom_check_function() {
    local device_name="$1"
    local device_ip="$2"
    local ssh_user="$3"
    local ssh_password="$4"

    echo "=========================================="
    echo "自定义检测功能"
    echo "=========================================="

    # 本地检测模式
    if [[ "$device_name" == "localhost" ]]; then
        echo "设备: localhost (本地主机)"
        echo ""

        log_info "开始自定义本地检测..."

        # 直接执行本地命令
        local result=$(your_local_command 2>/dev/null)

        if [[ $? -eq 0 && -n "$result" ]]; then
            echo "自定义检测成功"
            echo "$result"
            log_info "自定义本地检测成功"
            return 0
        else
            echo "自定义检测失败"
            log_error "自定义本地检测失败"
            return 1
        fi
    else
        # 远程检测模式
        echo "设备: $device_name ($device_ip)"
        echo ""

        log_info "开始自定义远程检测..."

        # 执行远程命令
        local result=$(ssh_execute "$device_ip" "$ssh_user" "$ssh_password" "22" "your_remote_command" 2>/dev/null)

        if [[ $? -eq 0 && -n "$result" ]]; then
            echo "自定义检测成功"
            echo "$result"
            log_info "自定义远程检测成功"
            return 0
        else
            echo "自定义检测失败"
            log_error "自定义远程检测失败"
            return 1
        fi
    fi
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 验证参数
    if ! validate_parameters "$@"; then
        exit 1
    fi

    # 执行检测
    if custom_check_function "$@"; then
        exit 0
    else
        exit 1
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

#### 8.1.3 集成到主脚本

在 `afw3000_check.sh` 的 `execute_local_detection()` 函数中添加：

```bash
# 自定义检测模块（本地模式）
log_info "执行自定义检测..."
local custom_check_output=""
custom_check_output=$(./modules/custom_check.sh localhost 2>&1)
local custom_exit_code=$?

# 保存结果到临时文件
local temp_custom_file="/tmp/afw3000_custom_result_localhost_$$"
echo "CUSTOM_CHECK_EXIT_CODE=$custom_exit_code" >"$temp_custom_file"
echo "CUSTOM_CHECK_OUTPUT_START" >>"$temp_custom_file"
echo "$custom_check_output" >>"$temp_custom_file"
echo "CUSTOM_CHECK_OUTPUT_END" >>"$temp_custom_file"
```

### 8.2 配置文件扩展方法

#### 8.2.1 扩展设备配置

在 `config/devices.conf` 中添加新设备：

```bash
# N4节点配置（新增设备）
N4_HOST="*************"
N4_PORT="22"
N4_USER="admin"
N4_PASSWORD="admin123"
N4_DESCRIPTION="新增Linux服务器"
```

#### 8.2.2 扩展检测配置

在 `config/check_config.conf` 中添加新参数：

```bash
# 自定义检测参数
CUSTOM_CHECK_TIMEOUT=60
CUSTOM_CHECK_RETRY=3
CUSTOM_CHECK_THRESHOLD=90
```

#### 8.2.3 扩展软件检测配置

在 `config/software_check.conf` 中添加新服务：

```bash
[custom_service]
name=自定义服务
type=service
category=自定义
check_cmd=systemctl is-active custom-service
version_cmd=custom-service --version
install_path=/opt/custom-service
description=自定义业务服务
```

### 8.3 二次开发指南

#### 8.3.1 开发环境搭建

```bash
# 1. 克隆项目
git clone https://github.com/example/afw3000-check.git
cd afw3000-check

# 2. 创建开发分支
git checkout -b feature/new-module

# 3. 设置开发环境
export AFW3000_DEV=1
export AFW3000_DEBUG=1
```

#### 8.3.2 代码规范

##### Shell 脚本规范

```bash
# 1. 文件头部注释
#!/bin/bash
# 模块名称
# 功能: 模块功能描述
# 版本: 1.0
# 作者: 开发者姓名

# 2. 函数命名规范
function_name() {
    local param1="$1"
    local param2="$2"

    # 函数实现
    return 0
}

# 3. 变量命名规范
LOCAL_VARIABLE="value"          # 局部变量大写
global_variable="value"         # 全局变量小写

# 4. 错误处理
if ! command; then
    log_error "命令执行失败"
    return 1
fi
```

##### 日志规范

```bash
# 使用统一的日志函数
log_info "信息日志"
log_warn "警告日志"
log_error "错误日志"

# 调试日志
if [[ "$DEBUG_MODE" == "true" ]]; then
    log_info "调试信息: $debug_info"
fi
```

#### 8.3.3 测试方法

##### 单元测试

```bash
# 创建测试脚本
touch tests/test_custom_module.sh
chmod +x tests/test_custom_module.sh

# 测试脚本示例
#!/bin/bash
source modules/custom_check.sh

# 测试函数
test_custom_function() {
    local result=$(custom_check_function "localhost" "" "" "")
    if [[ $? -eq 0 ]]; then
        echo "测试通过: custom_check_function"
    else
        echo "测试失败: custom_check_function"
    fi
}

# 运行测试
test_custom_function
```

##### 集成测试

```bash
# 测试完整流程
./afw3000_check.sh --debug

# 测试特定模块
./modules/custom_check.sh localhost

# 测试配置文件
bash -n config/devices.conf
```

#### 8.3.4 文档更新

##### 更新用户手册

```bash
# 1. 更新功能说明
# 2. 添加使用示例
# 3. 更新配置说明
# 4. 添加故障排查信息
```

##### 更新代码注释

```bash
# 1. 函数注释
# 2. 复杂逻辑注释
# 3. 配置项说明
# 4. 示例代码注释
```

#### 8.3.5 版本发布

##### 版本号规范

- **主版本号**：重大功能变更
- **次版本号**：新功能添加
- **修订版本号**：Bug 修复

##### 发布流程

```bash
# 1. 代码审查
git diff main..feature/new-module

# 2. 测试验证
./tests/run_all_tests.sh

# 3. 更新版本号
sed -i 's/SCRIPT_VERSION="1.0"/SCRIPT_VERSION="1.1"/' afw3000_check.sh

# 4. 提交代码
git add .
git commit -m "feat: 添加自定义检测模块"

# 5. 合并主分支
git checkout main
git merge feature/new-module

# 6. 创建标签
git tag -a v1.1 -m "版本 1.1 发布"
git push origin v1.1
```

---

## 附录

### A. 配置文件模板

#### A.1 devices.conf 模板

```bash
# AFW3000防火墙检测工具 - 设备配置文件
# 版本: 1.0

# N1节点配置（AFW3000防火墙）
N1_HOST="*************"
N1_PORT="36863"
N1_USER="admin"
N1_PASSWORD="admin@123"

# N2节点配置（Linux系统）
N2_HOST="**************"
N2_PORT="22"
N2_USER="root"
N2_PASSWORD="password123"
N2_BACKUP_USER="backup"
N2_BACKUP_PASSWORD="backup123"

# N3节点配置（Linux系统）
N3_HOST="*************"
N3_PORT="36863"
N3_USER="root"
N3_PASSWORD="password123"
```

#### A.2 check_config.conf 模板

```bash
# AFW3000防火墙检测工具 - 检测配置文件
# 版本: 1.0

# 网络检测参数
PING_COUNT=3
PING_TIMEOUT=5
CHECK_PORTS="80,163,443,36863"

# 系统状态阈值
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=80

# SSH连接参数
SSH_TIMEOUT=10
SSH_RETRY_COUNT=3

# 报告生成参数
REPORT_FORMAT="text"
INCLUDE_RAW_DATA=true
```

### B. 常用命令速查

#### B.1 基本使用

```bash
# 查看帮助
./afw3000_check.sh --help

# 查看版本
./afw3000_check.sh --version

# 检测所有节点
./afw3000_check.sh all

# 检测单个节点
./afw3000_check.sh n1
./afw3000_check.sh n2
./afw3000_check.sh n3

# 本地检测（默认行为）
./afw3000_check.sh

# 调试模式
./afw3000_check.sh --debug n1
./afw3000_check.sh --debug
```

#### B.2 故障排查

```bash
# 检查语法
bash -n afw3000_check.sh

# 检查权限
ls -la afw3000_check.sh
chmod +x afw3000_check.sh

# 检查配置
cat config/devices.conf
cat config/check_config.conf

# 清理临时文件
rm -f /tmp/afw3000_*

# 查看报告
ls -la report/
cat report/afw3000_check_*.txt
```

### C. 技术支持

#### C.1 联系方式

- **开发者**：dkyzheng
- **项目地址**：https://github.com/example/afw3000-check
- **问题反馈**：https://github.com/example/afw3000-check/issues

#### C.2 更新日志

- **v1.0**：初始版本，支持远程检测和本地检测
- **v1.1**：将本地检测设为默认行为，移除 `--local` 参数
- **v1.2**：优化报告格式，增强错误处理

---

_本文档最后更新时间：2025-07-29_
_文档版本：1.0_
