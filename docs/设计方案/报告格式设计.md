# AFW3000检测报告格式设计

## 概述

本文档定义AFW3000防火墙设备检测脚本的报告输出格式，确保报告内容简洁明了、便于阅读和存档。

## 报告文件规范

### 1. 文件命名规范

```bash
# 报告文件命名格式
afw3000_check_YYYYMMDD_HHMMSS.txt

# 示例
afw3000_check_20250722_143000.txt  # 2025年7月22日 14:30:00生成

# 存储位置
report/afw3000_check_YYYYMMDD_HHMMSS.txt
```

### 2. 文件编码和格式

```bash
文件属性：
├── 编码格式：UTF-8
├── 换行符：Unix格式 (LF)
├── 文件权限：644 (rw-r--r--)
└── 最大行宽：80字符
```

## 报告内容结构

### 1. 报告模板结构

```text
========================================
AFW3000防火墙设备检测报告
========================================

检测时间：YYYY-MM-DD HH:MM:SS
检测范围：[检测范围描述]
脚本版本：[版本号]

========================================
设备检测概况
========================================

总节点数：[数量]
检测成功：[数量]
检测失败：[数量]
检测成功率：[百分比]

========================================
设备详细检测结果
========================================

[设备检测结果详情]

================================================================================
原始命令输出附录
================================================================================

[原始命令输出数据]

================================================================================
附录结束
================================================================================

报告生成时间：YYYY-MM-DD HH:MM:SS
```

### 2. 详细内容格式

#### 2.1 数据显示规范

**未获取数据的显示规则：**
- 所有无法获取或不可用的数据字段统一显示为 `-` 符号
- 避免使用"未获取到"、"未知"、"N/A"等冗长描述
- 保持报告简洁直观，提高可读性

**树状结构符号规范：**
- 使用 `├──` 表示非最后一项的子项目
- 使用 `└──` 表示最后一项的子项目
- 确保缩进层级正确，便于阅读

**示例：**
```text
├── 内存：
│   ├── 总容量：8GB
│   └── 内存条列表：
│       ├── 内存条1: 型号=DDR4-2666, 容量=4GB, 类型=DDR4
│       └── 内存条2: 型号=-, 容量=4GB, 类型=DDR4
├── 存储：
│   ├── 总容量：256GB
│   └── 硬盘列表：
│       └── 硬盘1: 型号=Samsung SSD, 容量=256GB, 类型=SSD
```

#### 2.2 报告头部格式

**远程检测模式**：
```text
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-22 14:30:00
检测范围：所有节点 (N1, N2, N3)
脚本版本：1.0
```

**本地检测模式**：
```text
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-29 23:40:40
检测范围：本地主机 (AFW2000S-N1)
脚本版本：1.0
```

#### 2.2 设备概况格式
```text
========================================
设备检测概况
========================================

总节点数：3
检测成功：2
检测失败：1
检测成功率：66.7%
```

#### 2.3 设备详细结果格式

##### 2.3.1 远程检测格式
```text
========================================
设备详细检测结果
========================================

[N1节点 - *************]
├── 网络检测：正常
│   ├── snmp端口 163：开放
│   ├── https端口 443：开放
│   └── ssh端口 36863：关闭
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：E2000Q_NT06
│   │   ├── 制造商：Phytium
│   │   └── MCU版本：V15-0.0.13
│   ├── CPU：
│   │   ├── 型号：Phytium,FT-E2000Q
│   │   ├── 核数：4
│   │   └── 频率：core 0~1: 1500.0MHz, core 2~3: 2000.0MHz
│   ├── 内存：
│   │   ├── 总容量：4096M
│   │   └── 内存条列表：
│   │       └── 内存条1: 型号=DDR4-2400, 容量=4096M, 类型=DDR4
│   ├── 存储：
│   │   ├── 总容量：Flash: 29.50G, Disk: 469G
│   │   └── 硬盘列表：
│   │       ├── 硬盘1: 型号=eMMC Flash, 容量=29.50G, 类型=Flash
│   │       └── 硬盘2: 型号=SATA SSD, 容量=469G, 类型=SSD
│   └── 网口：
│       ├── 网口总数：3个
│       └── 网口列表：
│           ├── ge0: IP=*************/24, MAC=00:1a:2b:3c:4d:5e, 速率=1000Mbps, 状态=UP
│           ├── ge1: IP=*************/24, MAC=00:1a:2b:3c:4d:5f, 速率=1000Mbps, 状态=UP
│           └── ge2: 未配置IP, MAC=00:1a:2b:3c:4d:50, 速率=100Mbps, 状态=DOWN
├── 系统信息：正常
│   ├── 系统版本：
│   │   ├── 防火墙版本：V3.0 (Build Jul 8 2024 17:34:15)
│   │   ├── 固件版本：FW-V5.2-R6.0-NT-20240708-ALL.BIN
│   │   └── 平台：PLATFORM_NT06
│   ├── 系统状态：
│   │   ├── 运行时间：33天0小时20分钟
│   │   ├── CPU温度：34.0°C
│   │   ├── 主板温度：27.125°C
│   │   └── 启动方式：冷启动
│   └── 软件序列号：000007700124082924708402
└── 软件信息：正常
    └── Web服务：
        ├── 服务状态：正常运行
        ├── 访问地址：https://*************:443
        ├── 响应时间：45ms
        └── 模拟登录状态：登录成功

[N2节点 - *************]
├── 网络检测：正常
│   ├── 端口22：开放
│   ├── 端口80：开放
│   ├── 端口443：开放
│   └── 端口8080：开放
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：Dell Inc. OptiPlex 7070
│   │   ├── 制造商：Dell Inc.
│   │   └── BIOS版本：1.21.0
│   ├── CPU：
│   │   ├── 型号：Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz
│   │   ├── 核数：8
│   │   └── 频率：3000.0MHz
│   ├── 内存：
│   │   ├── 总容量：16GB
│   │   └── 内存条列表：
│   │       ├── 内存条1: 型号=DDR4-2666, 容量=8GB, 类型=DDR4
│   │       └── 内存条2: 型号=DDR4-2666, 容量=8GB, 类型=DDR4
│   ├── 存储：
│   │   ├── 总容量：512GB
│   │   └── 硬盘列表：
│   │       ├── 硬盘1: 型号=Samsung SSD 970 EVO, 容量=256GB, 类型=NVMe
│   │       └── 硬盘2: 型号=WD Blue, 容量=256GB, 类型=SSD
│   └── 网口：
│       ├── 网口总数：3个
│       └── 网口列表：
│           ├── eth0: IP=*************/24, MAC=52:54:00:12:34:56, 速率=1000Mbps, 状态=UP
│           ├── eth1: IP=*************/24, MAC=52:54:00:12:34:57, 速率=1000Mbps, 状态=UP
│           └── eth2: 未配置IP, MAC=53:54:00:12:34:58, 速率=100Mbps, 状态=DOWN
├── 系统信息：异常
│   ├── 系统版本：
│   │   ├── 发行版本：Ubuntu 20.04.6 LTS
│   │   ├── 内核版本：5.4.0-150-generic
│   │   └── 系统架构：x86_64
│   └── 系统状态：
│       ├── 系统负载：3.2 [超过阈值2.0]
│       ├── 运行时间：15 days, 2:45
│       └── 启动方式：正常启动
└── 软件信息：正常
    ├── 服务统计：
    │   ├── 检测服务：15
    │   ├── 安装成功：10
    │   └── 安装失败：5
    ├── 安全套件：
    │   ├── 安装成功：
    │   │   ├── watchdog v2.1.3 (运行中)
    │   │   ├── nmap 7.80 (已安装)
    │   │   ├── wireguard 1.0.20210914 (运行中)
    │   │   ├── salt 3004.2 (运行中)
    │   │   └── zabbix-agent 5.4.12 (运行中)
    │   └── 安装失败：
    │   │   ├── 安全代理 (未安装)
    │   │   └── socks5代理 (未安装)
    ├── 安全加固：已执行加固
    └── 其他软件：
        ├── 安装成功：
        │   └── 欧拉数据库 (已安装)
        └── 安装失败：
            └── nextTermal (未安装)

[N3节点 - *************]
├── 网络检测：异常
│   └── 连接失败：无法建立SSH连接
├── 硬件信息：跳过
│   └── 原因：网络检测失败
├── 系统信息：跳过
│   └── 原因：网络检测失败
└── 软件信息：跳过
    └── 原因：网络检测失败
```

##### 2.3.2 本地检测格式

本地检测模式用于检测运行脚本的本机系统，无需SSH连接，可获取更详细的硬件和系统信息。

```text
========================================
设备详细检测结果
========================================

[本地主机 - AFW2000S-N1 (*************)]
├── 网络检测：正常
│   ├── 端口22：关闭
│   ├── 端口80：关闭
│   └── 端口443：关闭
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：[主板型号信息]
│   │   ├── 制造商：[制造商信息]
│   │   └── BIOS版本：[BIOS版本信息]
│   ├── CPU：
│   │   ├── 型号：Intel(R) Core(TM) i5-6500 CPU @ 3.20GHz
│   │   ├── 核数：4
│   │   └── 频率：2028.320MHz
│   ├── 内存：
│   │   ├── 总容量：15787MB
│   │   └── 内存条列表：
│   │       ├── 内存条1: 型号=Samsung DDR4, 容量=8192MB, 类型=DDR4
│   │       └── 内存条2: 型号=Samsung DDR4, 容量=8192MB, 类型=DDR4
│   ├── 存储：
│   │   ├── 总容量：945GB
│   │   └── 硬盘列表：
│   │       └── 硬盘1: 型号=[存储设备型号], 容量=945GB, 类型=[存储类型]
│   └── 网口：
│       ├── 网口总数：2个接口
│       └── 网口列表：
│           ├── eth0: IP=*************/24, MAC=52:54:00:12:34:56, 速率=1000Mbps, 状态=UP
│           └── eth1: 未配置IP, MAC=52:54:00:12:34:57, 速率=1000Mbps, 状态=DOWN
├── 系统信息：正常
│   ├── 系统版本：
│   │   ├── 发行版本：AETHER YDSECLinux
│   │   ├── 内核版本：3.10.0-1160.118.1.yd7.x86_64
│   │   └── 系统架构：x86_64
│   └── 系统状态：
│       ├── 系统负载：0.00, 0.01, 0.05
│       ├── 内存使用率：2%
│       ├── 硬盘使用率：2%
│       └── 运行时间：6 days
└── 软件信息：异常
    ├── 服务统计：
    │   ├── 检测服务：12
    │   ├── 安装成功：4
    │   └── 安装失败：8
    ├── 安全套件：
    │   ├── 安装成功：
    │   │   ├── SSH服务
    │   │   └── 系统服务
    │   └── 安装失败：
    │       └── 防火墙服务
    ├── 安全加固：已执行加固
    └── 其他软件：
        ├── 安装成功：
        │   └── 基础工具
        └── 安装失败：
            └── 监控工具
```

**本地检测与远程检测的主要差异：**

1. **节点标识格式**：
   - 远程检测：`[N1节点 - *************]`
   - 本地检测：`[本地主机 - 主机名 (IP地址)]`

2. **检测范围描述**：
   - 远程检测：`检测范围：所有节点 (N1, N2, N3)` 或 `检测范围：N1节点`
   - 本地检测：`检测范围：本地主机 (主机名)`

3. **硬件信息获取**：
   - 远程检测：通过SSH执行命令获取，可能受到权限限制
   - 本地检测：直接访问本地系统，可获取更详细的硬件信息

4. **系统信息字段**：
   - 远程检测：根据设备类型显示不同字段（AFW3000设备显示防火墙版本等）
   - 本地检测：显示标准Linux系统信息（发行版本、内核版本、系统架构）

5. **网络检测方式**：
   - 远程检测：通过网络连接测试端口可达性
   - 本地检测：检测本地端口监听状态
```

#### 2.4 报告尾部格式
```text
报告生成时间：2025-07-22 14:35:00
```



## 报告生成函数设计

### 1. 报告生成主函数

```bash
# 生成完整检测报告
generate_report() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local report_file="report/afw3000_check_$timestamp.txt"
    local check_scope="$1"  # all/n1/n2/n3

    # 创建报告目录
    mkdir -p "report"

    # 生成报告
    {
        generate_report_header "$check_scope"
        generate_device_summary
        generate_device_details
        generate_report_footer
    } > "$report_file"

    echo "检测报告已生成: $report_file"
    return 0
}
```

### 2. 报告组件生成函数

```bash
# 生成报告头部
generate_report_header() {
    local check_scope="$1"
    local scope_desc
    
    case "$check_scope" in
        "all") scope_desc="所有节点 (N1, N2, N3)" ;;
        "n1")  scope_desc="N1节点" ;;
        "n2")  scope_desc="N2节点" ;;
        "n3")  scope_desc="N3节点" ;;
        *)     scope_desc="其他范围" ;;
    esac
    
    cat << EOF
AFW3000防火墙设备检测报告
========================================

检测时间：$(date '+%Y-%m-%d %H:%M:%S')
检测范围：$scope_desc
脚本版本：1.0

EOF
}

# 生成设备概况
generate_device_summary() {
    cat << EOF
========================================
设备检测概况
========================================

总设备数：$TOTAL_DEVICES
检测成功：$SUCCESS_DEVICES
检测失败：$FAILED_DEVICES
成功率：$SUCCESS_RATE%

EOF
}

# 生成设备详细信息
generate_device_details() {
    cat << EOF
========================================
设备详细检测结果
========================================

$DEVICE_DETAILS

EOF
}



# 生成报告尾部
generate_report_footer() {
    cat << EOF
报告生成时间：$(date '+%Y-%m-%d %H:%M:%S')
EOF
}
```

## 报告输出示例

```text
========================================
AFW3000防火墙设备检测报告
========================================

检测时间：2025-07-22 14:30:00
检测范围：所有节点 (N1, N2, N3)
脚本版本：1.0

========================================
设备检测概况
========================================

总节点数：3
检测成功：2
检测失败：1
成功率：66.7%

========================================
设备详细检测结果
========================================

[N1节点 - *************]
├── 网络检测：正常
│   ├── snmp端口 163：开放
│   ├── https端口 443：开放
│   └── ssh端口 36863：关闭
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：E2000Q_NT06
│   │   ├── 制造商：Phytium
│   │   └── MCU版本：V15-0.0.13
│   ├── CPU：
│   │   ├── 型号：Phytium,FT-E2000Q
│   │   ├── 核数：4
│   │   └── 频率：core 0~1: 1500.0MHz, core 2~3: 2000.0MHz
│   ├── 内存：
│   │   ├── 总容量：4096M
│   │   └── 内存条列表：
│   │       └── 内存条1: 型号=DDR4-2400, 容量=4096M, 类型=DDR4
│   ├── 存储：
│   │   ├── 总容量：Flash: 29.50G, Disk: 469G
│   │   └── 硬盘列表：
│   │       ├── 硬盘1: 型号=eMMC Flash, 容量=29.50G, 类型=Flash
│   │       └── 硬盘2: 型号=SATA SSD, 容量=469G, 类型=SSD
│   └── 网口：
│       ├── 网口总数：6个千兆网口
│       └── 网口列表：
│           ├── ge0: IP=*************/24, MAC=00:1a:2b:3c:4d:5e, 速率=1000Mbps
│           ├── ge1: IP=*************/24, MAC=00:1a:2b:3c:4d:5f, 速率=1000Mbps
│           └── ge2-ge5: 未配置
├── 系统信息：正常
│   ├── 系统版本：
│   │   ├── 防火墙版本：V3.0 (Build Jul 8 2024 17:34:15)
│   │   ├── 固件版本：FW-V5.2-R6.0-NT-20240708-ALL.BIN
│   │   └── 平台：PLATFORM_NT06
│   ├── 系统状态：
│   │   ├── 运行时间：33天0小时20分钟
│   │   ├── CPU温度：34.0°C
│   │   ├── 主板温度：27.125°C
│   │   └── 启动方式：冷启动
│   └── 软件序列号：000007700124082924708402
└── 软件信息：正常
    └── Web服务：
        ├── 服务状态：正常运行
        ├── 访问地址：https://*************:443
        ├── 响应时间：45ms
        └── 模拟登录状态：登录成功

[N2节点 - *************]
├── 网络检测：正常
│   ├── 端口22：开放
│   ├── 端口80：开放
│   ├── 端口443：开放
│   └── 端口8080：开放
├── 硬件信息：正常
│   ├── 主板：
│   │   ├── 型号：Dell Inc. OptiPlex 7070
│   │   ├── 制造商：Dell Inc.
│   │   └── BIOS版本：1.21.0
│   ├── CPU：
│   │   ├── 型号：Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz
│   │   ├── 核数：8
│   │   └── 频率：3000.0MHz
│   ├── 内存：
│   │   ├── 总容量：16GB
│   │   └── 内存条列表：
│   │       ├── 内存条1: 型号=DDR4-2666, 容量=8GB, 类型=DDR4
│   │       └── 内存条2: 型号=DDR4-2666, 容量=8GB, 类型=DDR4
│   ├── 存储：
│   │   ├── 总容量：512GB
│   │   └── 硬盘列表：
│   │       ├── 硬盘1: 型号=Samsung SSD 970 EVO, 容量=256GB, 类型=NVMe
│   │       └── 硬盘2: 型号=WD Blue, 容量=256GB, 类型=SSD
│   └── 网口：
│       ├── 网口总数：2个接口
│       └── 网口列表：
│           ├── eth0: IP=*************/24, MAC=52:54:00:12:34:56, 速率=1000Mbps
│           └── lo: IP=127.0.0.1/8, MAC=00:00:00:00:00:00, 速率=-
├── 系统信息：异常
│   ├── 系统版本：
│   │   ├── 发行版本：Ubuntu 20.04.6 LTS
│   │   ├── 内核版本：5.4.0-150-generic
│   │   └── 系统架构：x86_64
│   └── 系统状态：
│       ├── 系统负载：3.2 [超过阈值2.0]
│       ├── 运行时间：15 days, 2:45
│       └── 启动方式：正常启动
└── 软件信息：正常
    ├── 服务统计：
    │   ├── 检测服务：15
    │   ├── 安装成功：10
    │   └── 安装失败：5
    ├── 安全套件：
    │   ├── 安装成功：
    │   │   ├── watchdog v2.1.3 (运行中)
    │   │   ├── nmap 7.80 (已安装)
    │   │   ├── wireguard 1.0.20210914 (运行中)
    │   │   ├── salt 3004.2 (运行中)
    │   │   └── zabbix-agent 5.4.12 (运行中)
    │   └── 安装失败：
    │   │   ├── 安全代理 (未安装)
    │   │   └── socks5代理 (未安装)
    ├── 安全加固：已执行加固
    └── 其他软件：
        ├── 安装成功：
        │   ├── SVN 1.14.1 (已安装)
        │   └── Jenkins JDK 11.0.16 (已安装)
        └── 安装失败：
            ├── Sysdig监控工具 (未安装)
            └── BEEP中继服务 (未安装)

[N3节点 - *************]
├── 网络检测：异常
│   └── 连接失败：无法建立SSH连接
├── 硬件信息：跳过
│   └── 原因：网络检测失败
├── 系统信息：跳过
│   └── 原因：网络检测失败
└── 软件信息：跳过
    └── 原因：网络检测失败

================================================================================
原始命令输出附录
================================================================================

本附录包含获取各配置指标的原始命令执行结果，供技术分析和对照验证使用。
数据按硬件配置、系统信息、网络配置分类展示，去除了检测流程信息，专注于核心命令输出。

[N1节点原始命令输出]
========================================

硬件配置原始数据
--------------
# CPU信息
Platform: AFW3000-X1
Model: AFW3000
Product: 安博通防火墙
CPU: Phytium,FT-E2000Q
Cores: 4
Frequency: core 0~1: 1500.0MHz, core 2~3: 2000.0MHz

系统信息原始数据
--------------
## show ver输出:
Hostname: AFW3000-FW01
Version: V3.0 (Build Jul 8 2024 17:34:15)
Firmware: FW-V5.2-R6.0-NT-20240708-ALL.BIN
Platform: PLATFORM_NT06
System uptime: 33天0小时20分钟
Software S/N: 000007700124082924708402

网络配置原始数据
--------------
## show interface输出:
ge0: IP=*************/24, MAC=00:1a:2b:3c:4d:5e, 速率=1000Mbps, Status=UP
ge1: IP=*************/24, MAC=00:1a:2b:3c:4d:5f, 速率=1000Mbps, Status=UP
ge2-ge5: Not configured

========================================

[N2节点原始命令输出]
========================================

硬件配置原始数据
--------------
# CPU信息
## lscpu输出:
Architecture:        x86_64
CPU op-mode(s):      32-bit, 64-bit
Byte Order:          Little Endian
CPU(s):              8
On-line CPU(s) list: 0-7
Thread(s) per core:  1
Core(s) per socket:  8
Socket(s):           1
Vendor ID:           GenuineIntel
CPU family:          6
Model:               158
Model name:          Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz
Stepping:            13
CPU MHz:             3000.000
BogoMIPS:            6000.00
L1d cache:           32K
L1i cache:           32K
L2 cache:            256K
L3 cache:            12288K

# 内存信息
## free -h输出:
              total        used        free      shared  buff/cache   available
Mem:           16Gi        3.2Gi        8.1Gi        128Mi        4.7Gi        12Gi
Swap:          2.0Gi          0B        2.0Gi

## dmidecode -t memory输出:
Memory Device
    Array Handle: 0x0001
    Error Information Handle: Not Provided
    Total Width: 64 bits
    Data Width: 64 bits
    Size: 8192 MB
    Form Factor: DIMM
    Set: None
    Locator: DIMM_A1
    Bank Locator: BANK 0
    Type: DDR4
    Type Detail: Synchronous
    Speed: 2666 MT/s
    Manufacturer: Samsung
    Serial Number: ********
    Asset Tag: 9********0
    Part Number: M378A1K43CB2-CTD

Memory Device
    Array Handle: 0x0001
    Error Information Handle: Not Provided
    Total Width: 64 bits
    Data Width: 64 bits
    Size: 8192 MB
    Form Factor: DIMM
    Set: None
    Locator: DIMM_A2
    Bank Locator: BANK 1
    Type: DDR4
    Type Detail: Synchronous
    Speed: 2666 MT/s
    Manufacturer: Samsung
    Serial Number: ********
    Asset Tag: **********
    Part Number: M378A1K43CB2-CTD

# 存储信息
## lsblk输出:
NAME   MAJ:MIN RM   SIZE RO TYPE MOUNTPOINT
sda      8:0    0   256G  0 disk
└─sda1   8:1    0   256G  0 part /
sdb      8:16   0   256G  0 disk
└─sdb1   8:17   0   256G  0 part /home

## df -h输出:
Filesystem      Size  Used Avail Use% Mounted on
/dev/sda1       256G   64G  180G  27% /
/dev/sdb1       256G   32G  212G  14% /home
tmpfs           8.0G     0  8.0G   0% /dev/shm

系统信息原始数据
--------------
## uname -a输出:
Linux server02 5.4.0-150-generic #167-Ubuntu SMP Mon May 15 17:35:05 UTC 2023 x86_64 x86_64 x86_64 GNU/Linux

## /etc/os-release输出:
NAME="Ubuntu"
VERSION="20.04.6 LTS (Focal Fossa)"
ID=ubuntu
ID_LIKE=debian
PRETTY_NAME="Ubuntu 20.04.6 LTS"
VERSION_ID="20.04"
HOME_URL="https://www.ubuntu.com/"
SUPPORT_URL="https://help.ubuntu.com/"
BUG_REPORT_URL="https://bugs.launchpad.net/ubuntu/"
PRIVACY_POLICY_URL="https://www.ubuntu.com/legal/terms-and-policies/privacy-policy"
VERSION_CODENAME=focal
UBUNTU_CODENAME=focal

## uptime输出:
 14:30:00 up 15 days,  2:45,  3 users,  load average: 3.20, 2.85, 2.90

网络配置原始数据
--------------
## ip addr输出:
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host
       valid_lft forever preferred_lft forever
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP group default qlen 1000
    link/ether 52:54:00:12:34:56 brd ff:ff:ff:ff:ff:ff
    inet *************/24 brd ************* scope global dynamic eth0
       valid_lft 86400sec preferred_lft 86400sec
    inet6 fe80::5054:ff:fe12:3456/64 scope link
       valid_lft forever preferred_lft forever

## ping测试结果:
PING ************* (*************) 56(84) bytes of data.
64 bytes from *************: icmp_seq=1 time=0.045 ms
64 bytes from *************: icmp_seq=2 time=0.042 ms
64 bytes from *************: icmp_seq=3 time=0.041 ms
--- ************* ping statistics ---
3 packets transmitted, 3 received, 0% packet loss, time 2048ms
rtt min/avg/max/mdev = 0.041/0.043/0.045/0.002 ms

========================================

[N3节点原始命令输出]
========================================

硬件配置原始数据
--------------
无硬件配置数据

系统信息原始数据
--------------
无系统信息数据

网络配置原始数据
--------------
无网络配置数据

========================================

================================================================================
附录结束
================================================================================

报告生成时间：2025-07-22 14:35:00
```



#### 2.5 原始命令输出附录格式

原始命令输出附录是报告的重要组成部分，位于设备详细检测结果之后，包含获取各配置指标的原始命令执行结果，供技术分析和对照验证使用。

##### 2.5.1 附录整体结构

```text
================================================================================
原始命令输出附录
================================================================================

本附录包含获取各配置指标的原始命令执行结果，供技术分析和对照验证使用。
数据按硬件配置、系统信息、网络配置分类展示，去除了检测流程信息，专注于核心命令输出。

[各节点原始命令输出]

================================================================================
附录结束
================================================================================

报告生成时间：YYYY-MM-DD HH:MM:SS
```

##### 2.5.2 节点原始数据格式

每个检测节点的原始数据按以下格式组织：

**远程检测节点**：
```text
[N1节点原始命令输出]
========================================

硬件配置原始数据
--------------
[硬件相关命令的原始输出]

系统信息原始数据
--------------
[系统相关命令的原始输出]

网络配置原始数据
--------------
[网络相关命令的原始输出]

========================================
```

**本地检测节点**：
```text
[本地主机节点原始命令输出]
========================================

硬件配置原始数据
--------------
[本地硬件相关命令的原始输出]

系统信息原始数据
--------------
[本地系统相关命令的原始输出]

网络配置原始数据
--------------
[本地网络相关命令的原始输出]

========================================
```

##### 2.5.3 AFW3000设备原始数据内容

AFW3000防火墙设备（N1节点）的原始数据包含：

**硬件配置原始数据：**
- `show ver` 命令中的CPU和处理器相关信息

**系统信息原始数据：**
- `show ver` 命令的完整输出，包含版本、固件、平台等信息

**网络配置原始数据：**
- `show interface` 命令的完整输出，包含所有网口配置信息

##### 2.5.4 Linux系统原始数据内容

Linux系统（N2/N3节点）的原始数据包含：

**硬件配置原始数据：**
- `lscpu` 命令输出（CPU信息）
- `free -h` 命令输出（内存信息）
- `dmidecode -t memory` 命令输出（内存详细信息）
- `lsblk` 命令输出（存储设备信息）
- `df -h` 命令输出（磁盘使用情况）

**系统信息原始数据：**
- `uname -a` 命令输出（内核信息）
- `cat /etc/os-release` 命令输出（发行版信息）
- `uptime` 命令输出（系统运行时间和负载）

**网络配置原始数据：**
- `ip addr` 命令输出（网络接口信息）
- `ping` 测试结果（网络连通性）

##### 2.5.5 本地检测原始数据内容

本地检测的原始数据包含：

**硬件配置原始数据：**
- `lscpu` 命令输出（CPU详细信息）
- `free -h` 命令输出（内存使用情况）
- `dmidecode -t memory` 命令输出（内存条详细信息）
- `lsblk` 命令输出（存储设备信息）
- `df -h` 命令输出（磁盘使用情况）

**系统信息原始数据：**
- `uname -a` 命令输出（系统内核信息）
- `/etc/os-release` 文件内容（操作系统信息）
- `uptime` 命令输出（系统运行时间和负载）

**网络配置原始数据：**
- `ip addr` 命令输出（网络接口信息）
- `netstat -tuln` 命令输出（端口监听状态）

##### 2.5.6 数据缺失处理

当某类数据无法获取时，显示相应的提示信息：
- `无硬件配置数据`
- `无系统信息数据`
- `无网络配置数据`

---

**报告设计原则：**
- **简洁明了**：信息层次清晰，便于快速理解
- **标准格式**：统一的格式规范，便于自动化处理
- **详细完整**：包含所有必要的检测信息
- **易于存档**：文件命名包含时间戳，便于历史记录
- **可读性强**：使用树形结构和符号，提高可读性
