# jq 速查表

## 🔧 命令行选项
```bash
jq -r '.field'          # 原始输出（去掉引号）
jq -c '.field'          # 紧凑输出（一行）
jq -n 'expression'      # 不读取输入
jq -s '.[]'             # 将多个JSON合并为数组
jq -e '.field'          # 根据输出设置退出码
jq --arg var value      # 传递变量
jq -f script.jq         # 从文件读取jq脚本
```

## 📍 基础访问
```bash
.field                  # 获取字段
.nested.field          # 嵌套字段
.array[0]              # 数组第一个元素
.array[-1]             # 数组最后一个元素
.array[1:3]            # 数组切片
.array[]               # 数组所有元素
.[]                    # 对象所有值
```

## 🔍 过滤选择
```bash
select(.age > 30)                    # 条件过滤
select(.active == true)              # 布尔值过滤
select(.name | contains("张"))       # 字符串包含
select(.email | test("@gmail"))      # 正则匹配
select(has("field"))                 # 字段存在检查
select(.field | type == "string")    # 类型检查
```

## 🔄 数据转换
```bash
map(.field)                          # 提取字段
map(select(.active))                 # 过滤映射
map(. + {new: "value"})             # 添加字段
map(del(.field))                     # 删除字段
{name, age}                          # 对象构造
{(.key): .value}                     # 动态键名
. + {new_field: "value"}            # 合并对象
```

## 📊 聚合操作
```bash
length                               # 数组/对象长度
add                                  # 数组求和
max / min                            # 最大/最小值
sort / sort_by(.field)              # 排序
reverse                              # 反转
unique                               # 去重
group_by(.field)                     # 分组
flatten                              # 数组扁平化
```

## 🎯 条件表达式
```bash
if .age > 30 then "老" else "年轻" end    # if-then-else
.field // "默认值"                        # 空值合并
.field?                                   # 安全访问
try .field catch "错误"                   # 异常处理
```

## 🔢 数学运算
```bash
.a + .b                              # 加法
.a - .b                              # 减法
.a * .b                              # 乘法
.a / .b                              # 除法
.a % .b                              # 取模
```

## 📝 字符串操作
```bash
.str | length                        # 字符串长度
.str | contains("sub")               # 包含检查
.str | startswith("pre")             # 前缀检查
.str | endswith("suf")               # 后缀检查
.str | split(",")                    # 分割字符串
.str | ltrimstr("pre")               # 去除前缀
.str | rtrimstr("suf")               # 去除后缀
.str | ascii_downcase                # 转小写
.str | ascii_upcase                  # 转大写
```

## 🔄 类型转换
```bash
tonumber                             # 转数字
tostring                             # 转字符串
type                                 # 获取类型
```

## 📋 常用模式

### 查找和过滤
```bash
# 查找特定用户
.users[] | select(.id == 1)

# 多条件过滤
.users[] | select(.age > 25 and .active == true)

# 字符串模糊匹配
.users[] | select(.name | test("张|李"))
```

### 数据统计
```bash
# 计算平均值
[.users[].age] | add / length

# 按字段分组统计
.users | group_by(.city) | map({city: .[0].city, count: length})

# 条件计数
.users | map(select(.active)) | length
```

### 数据重构
```bash
# 创建摘要
.users[] | {name, email, city: .address.city}

# 嵌套数据扁平化
.users[] as $user | $user.projects[] | {user: $user.name, project: .name}

# 数据透视
.users | group_by(.city) | map({city: .[0].city, users: map(.name)})
```

## ⚠️ 常见陷阱

```bash
# ❌ 错误：字符串数字比较
.age > "30"

# ✅ 正确：类型一致
.age > 30

# ❌ 错误：未处理null
.users[].score | add

# ✅ 正确：过滤null
[.users[].score | select(. != null)] | add

# ❌ 错误：假设数组有元素
.users[0].name

# ✅ 正确：安全访问
.users[0]?.name // "无数据"
```

## 🚀 高级技巧

### 自定义函数
```bash
def avg(stream): [stream] | add / length;
def safe_divide(a; b): if b == 0 then 0 else a / b end;
```

### 递归处理
```bash
def find_values(key): 
  if type == "object" then
    if has(key) then [.[key]] else [] end +
    [.[] | find_values(key)] | flatten
  elif type == "array" then
    [.[] | find_values(key)] | flatten
  else [] end;
```

### 流式处理
```bash
# 处理大文件
jq -c '.users[]' large.json | while read line; do
    echo "$line" | jq '.name'
done
```

---
💡 **提示**：这个速查表涵盖了95%的日常使用场景。遇到复杂问题时，组合这些基础操作通常能解决！
