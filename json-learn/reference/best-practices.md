# JSON处理最佳实践

## 🎯 核心原则

### 1. 工具选择
- ✅ **优先使用jq**：功能强大，专为JSON设计
- ✅ **备选yq**：需要处理多种格式时
- ❌ **避免传统工具**：grep/sed/awk容易出错

### 2. 错误处理
```bash
# ✅ 检查文件存在
[[ -f "data.json" ]] || { echo "文件不存在"; exit 1; }

# ✅ 验证JSON格式
jq empty data.json 2>/dev/null || { echo "JSON格式错误"; exit 1; }

# ✅ 安全字段访问
jq '.field // "默认值"' data.json

# ✅ 异常处理
jq 'try .complex.path catch "路径不存在"' data.json
```

### 3. 性能优化
```bash
# ❌ 错误：重复解析
name=$(jq -r '.name' data.json)
age=$(jq -r '.age' data.json)

# ✅ 正确：一次解析
eval $(jq -r '@sh "name=\(.name) age=\(.age)"' data.json)

# ✅ 大文件流式处理
jq -c '.users[]' large.json | while read line; do
    echo "$line" | jq '.name'
done
```

## 🔒 安全考虑

### 输入验证
```bash
# 验证数据结构
validate_user() {
    jq -e 'has("name") and has("email") and (.age | type) == "number"' "$1" >/dev/null
}

# 防止注入攻击
# ❌ 危险
jq ".name = \"$user_input\"" data.json

# ✅ 安全
jq --arg input "$user_input" '.name = $input' data.json
```

### 数据类型检查
```bash
# 确保数字类型
jq 'if (.age | type) == "number" then .age else (.age | tonumber) end'

# 处理null值
jq '[.users[].score | select(. != null)] | add'
```

## 📝 代码组织

### 模块化函数
```bash
# 可重用的查询函数
get_active_users() {
    jq '.users[] | select(.active == true)' "$1"
}

get_user_by_id() {
    local id="$1" file="$2"
    jq --arg id "$id" '.users[] | select(.id == ($id | tonumber))' "$file"
}
```

### 复杂查询分解
```bash
# 分步处理复杂逻辑
process_data() {
    local file="$1"
    
    # 步骤1：数据清洗
    jq 'del(.users[].internal_data)' "$file" > temp1.json
    
    # 步骤2：数据转换
    jq '.users |= map(. + {full_name: "\(.first_name) \(.last_name)"})' temp1.json > temp2.json
    
    # 步骤3：最终输出
    jq '.users | sort_by(.age)' temp2.json
    
    # 清理
    rm -f temp1.json temp2.json
}
```

## 🐛 调试技巧

### 逐步调试
```bash
# 查看数据结构
jq '.users[0] | keys' data.json

# 查看数据类型
jq '.users[0] | to_entries | map({key, type: (.value | type)})' data.json

# 使用debug输出
jq '.users[] | debug | select(.age > 30)' data.json
```

### 测试查询
```bash
# 先测试单个元素
jq '.users[0] | your_complex_query' data.json

# 再应用到所有元素
jq '.users[] | your_complex_query' data.json
```

## ⚡ 常见模式

### 数据验证模式
```bash
# 检查必需字段
check_required_fields() {
    jq -e '.users[] | select(has("name") and has("email"))' "$1" >/dev/null
}

# 数据完整性检查
jq '.users | map(select(.email | test("@") | not)) | if length > 0 then error("无效邮箱") else empty end'
```

### 报告生成模式
```bash
# 生成摘要报告
generate_summary() {
    jq '{
        total_users: (.users | length),
        active_users: (.users | map(select(.active)) | length),
        avg_age: ([.users[].age] | add / length | floor),
        cities: (.users | map(.address.city) | unique | sort)
    }' "$1"
}
```

### 数据转换模式
```bash
# CSV导出
export_to_csv() {
    echo "姓名,年龄,城市,状态"
    jq -r '.users[] | [.name, .age, .address.city, (.active | if . then "活跃" else "非活跃" end)] | @csv' "$1"
}
```

## 🚨 常见错误

### 类型错误
```bash
# ❌ 字符串数字比较
jq '.age > "30"'

# ✅ 正确的数字比较
jq '.age > 30'
jq '(.age | tonumber) > 30'  # 如果age可能是字符串
```

### 空值处理
```bash
# ❌ 未处理null导致错误
jq '.users[].projects[].score | add'

# ✅ 正确处理null
jq '[.users[].projects[].score | select(. != null)] | add'
```

### 数组访问
```bash
# ❌ 假设数组有元素
jq '.users[0].name'

# ✅ 安全访问
jq '.users[0]?.name // "无用户"'
jq 'if (.users | length) > 0 then .users[0].name else "无用户" end'
```

## 📊 性能建议

### 大数据处理
```bash
# 使用流式API处理超大文件
jq --stream 'select(.[0][0] == "users" and .[0][1] != null)'

# 限制输出数量
jq '.users[:10]'  # 只处理前10个用户

# 早期过滤
jq '.users[] | select(.active) | select(.age > 30)'  # 先过滤再处理
```

### 内存优化
```bash
# 避免创建大型中间对象
# ❌ 内存消耗大
jq '.users | map(select(.active)) | map(.name)'

# ✅ 流式处理
jq '.users[] | select(.active) | .name'
```

## 🎯 总结清单

### 开发时检查
- [ ] 是否使用了jq而不是传统工具？
- [ ] 是否处理了null值和错误情况？
- [ ] 是否使用了安全的参数传递？
- [ ] 复杂查询是否分解为可读的步骤？

### 生产环境检查
- [ ] 是否有适当的错误处理？
- [ ] 是否考虑了性能影响？
- [ ] 是否有日志记录？
- [ ] 是否有数据验证？

---
💡 **记住**：好的JSON处理脚本应该是安全、高效、可维护的。遵循这些最佳实践可以避免90%的常见问题！
