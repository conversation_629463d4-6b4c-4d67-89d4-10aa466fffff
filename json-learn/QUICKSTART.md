# jq 快速上手指南

## 🚀 3分钟快速开始

### 1. 安装jq
```bash
# Ubuntu/Debian
sudo apt install jq

# CentOS/RHEL
sudo yum install jq

# 验证
jq --version
```

### 2. 基础语法
```bash
# 获取字段
echo '{"name": "张三", "age": 30}' | jq '.name'
# 输出: "张三"

# 获取数组元素
echo '[1,2,3]' | jq '.[0]'
# 输出: 1

# 过滤数据
echo '[{"name":"张三","age":30},{"name":"李四","age":25}]' | jq '.[] | select(.age > 28)'
# 输出: {"name":"张三","age":30}
```

### 3. 常用操作
```bash
# 提取多个字段
jq '{name, age}' data.json

# 数组操作
jq '.users | length' data.json          # 数组长度
jq '.users | map(.name)' data.json      # 提取所有name

# 条件过滤
jq '.users[] | select(.active == true)' data.json
```

## 📚 学习路径

1. **运行示例** (10分钟)
   ```bash
   ./examples/basic-usage.sh
   ./examples/common-patterns.sh
   ```

2. **查看速查表** (5分钟)
   ```bash
   cat reference/jq-cheatsheet.md
   ```

3. **学习最佳实践** (按需)
   ```bash
   cat reference/best-practices.md
   ```

## 💡 核心要点

- **基础语法**: `.field`、`.array[]`、`select()`、`map()`
- **常用选项**: `-r`(原始输出)、`-c`(紧凑)
- **安全访问**: 使用 `//` 提供默认值
- **管道操作**: 使用 `|` 连接多个操作

---
🎯 **目标**: 15分钟内掌握90%的日常JSON处理需求！
