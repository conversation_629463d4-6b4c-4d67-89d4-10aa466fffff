# jq 安装指南

## 🚀 快速安装

### Ubuntu/Debian

```bash
sudo apt update
sudo apt install jq
jq --version
```

### CentOS/RHEL

```bash
# CentOS 7/RHEL 7
sudo yum install epel-release
sudo yum install jq

# CentOS 8+/RHEL 8+
sudo dnf install epel-release
sudo dnf install jq

# 验证安装
jq --version
```

## 📦 二进制安装 (备用方案)

```bash
# 下载并安装最新版本
sudo wget -O /usr/local/bin/jq https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64
sudo chmod +x /usr/local/bin/jq
jq --version
```

---

## ✅ 验证安装

```bash
# 检查版本
jq --version

# 简单测试
echo '{"name": "test"}' | jq '.name'
# 应该输出: "test"
```

---

💡 **推荐**: 优先使用包管理器安装，简单可靠。如果包管理器版本过旧，再使用二进制安装。
