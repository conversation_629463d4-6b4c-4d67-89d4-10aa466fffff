# jq 安装部署指南 - Linux 生产环境

## 🚀 Linux 系统快速安装

### Ubuntu/Debian 系统

```bash
# 更新包索引
sudo apt update

# 安装jq (推荐方式)
sudo apt install jq

# 验证安装
jq --version

# 如果需要最新版本
sudo apt install software-properties-common
wget -qO - https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64 | sudo tee /usr/local/bin/jq > /dev/null
sudo chmod +x /usr/local/bin/jq
```

### CentOS/RHEL 系统

```bash
# CentOS 7/8, RHEL 7/8
# 启用EPEL仓库
sudo yum install epel-release
sudo yum install jq

# CentOS Stream 8/9, RHEL 8/9
sudo dnf install epel-release
sudo dnf install jq

# 验证安装
jq --version

# 如果需要最新版本
sudo wget -O /usr/local/bin/jq https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64
sudo chmod +x /usr/local/bin/jq
```

### Amazon Linux

```bash
# Amazon Linux 2
sudo yum install jq

# Amazon Linux 2023
sudo dnf install jq

# 验证安装
jq --version
```

### 其他 Linux 发行版

```bash
# Alpine Linux
apk add jq

# Arch Linux
sudo pacman -S jq

# openSUSE
sudo zypper install jq

# 验证安装
jq --version
```

## 📦 生产环境部署方式

### 二进制文件直接下载 (推荐用于生产环境)

```bash
# 下载最新版本 (Linux x86_64)
sudo wget -O /usr/local/bin/jq https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64

# 设置执行权限
sudo chmod +x /usr/local/bin/jq

# 创建软链接 (可选)
sudo ln -sf /usr/local/bin/jq /usr/bin/jq

# 验证安装
jq --version

# 检查文件完整性 (可选)
sha256sum /usr/local/bin/jq
```

### 离线安装包部署

```bash
# 1. 在有网络的机器上下载
wget https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64

# 2. 传输到目标服务器
scp jq-linux-amd64 user@server:/tmp/

# 3. 在目标服务器上安装
sudo mv /tmp/jq-linux-amd64 /usr/local/bin/jq
sudo chmod +x /usr/local/bin/jq
```

### 源码编译安装 (适用于特殊需求)

```bash
# 安装编译依赖
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install autoconf automake libtool make git build-essential

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install autoconf automake libtool make git

# 下载源码
git clone https://github.com/jqlang/jq.git
cd jq

# 编译安装
git submodule update --init
autoreconf -i
./configure --with-oniguruma=builtin --prefix=/usr/local
make -j$(nproc)
sudo make install

# 更新动态链接库缓存
sudo ldconfig

# 验证安装
jq --version
```

## 🐳 Docker 环境

### 使用官方 Docker 镜像

```bash
# 拉取镜像
docker pull ghcr.io/jqlang/jq:latest

# 使用示例 - 处理本地文件
docker run --rm -i ghcr.io/jqlang/jq:latest < data.json '.field'

# 挂载目录处理文件
docker run --rm -v "$PWD:$PWD" -w "$PWD" ghcr.io/jqlang/jq:latest '.field' data.json

# 创建别名方便使用
alias jq='docker run --rm -i -v "$PWD:$PWD" -w "$PWD" ghcr.io/jqlang/jq:latest'
```

### 在 Dockerfile 中安装

```dockerfile
# Alpine 基础镜像
FROM alpine:latest
RUN apk add --no-cache jq

# Ubuntu 基础镜像
FROM ubuntu:20.04
RUN apt-get update && apt-get install -y jq && rm -rf /var/lib/apt/lists/*

# 使用官方 jq 镜像作为基础
FROM ghcr.io/jqlang/jq:latest
```

## 🔍 环境验证

### 基础验证

```bash
# 检查版本
jq --version
# 期望输出: jq-1.7 或更高版本

# 检查帮助信息
jq --help

# 简单功能测试
echo '{"name": "test", "value": 42}' | jq '.name'
# 期望输出: "test"
```

### 完整功能测试

```bash
# 创建测试脚本
cat > test_jq.sh << 'EOF'
#!/bin/bash
echo "=== jq 功能测试 ==="

# 测试1: 基础JSON解析
echo "测试1: 基础解析"
echo '{"name": "张三", "age": 30}' | jq '.name' || echo "❌ 基础解析失败"

# 测试2: 数组操作
echo "测试2: 数组操作"
echo '[1,2,3,4,5]' | jq 'map(. * 2)' || echo "❌ 数组操作失败"

# 测试3: 复杂查询
echo "测试3: 复杂查询"
echo '{"users":[{"name":"张三","age":30},{"name":"李四","age":25}]}' | jq '.users[] | select(.age > 28)' || echo "❌ 复杂查询失败"

# 测试4: 错误处理
echo "测试4: 错误处理"
echo '{"data": null}' | jq '.data // "默认值"' || echo "❌ 错误处理失败"

echo "✅ 所有测试完成"
EOF

chmod +x test_jq.sh
./test_jq.sh
```

## ⚠️ 常见问题排查

### 问题 1: 命令未找到

```bash
# 错误信息: command not found: jq
# 解决方案:
which jq                    # 检查是否在PATH中
echo $PATH                  # 检查PATH环境变量
sudo find / -name jq 2>/dev/null  # 查找jq文件位置

# 如果找到但不在PATH中，添加到PATH
export PATH=$PATH:/path/to/jq/directory
```

### 问题 2: 权限问题

```bash
# 错误信息: Permission denied
# 解决方案:
ls -la $(which jq)          # 检查文件权限
sudo chmod +x $(which jq)  # 添加执行权限
```

### 问题 3: 版本过旧

```bash
# 检查当前版本
jq --version

# 升级方法 (macOS)
brew upgrade jq

# 升级方法 (Ubuntu)
sudo apt update && sudo apt upgrade jq

# 升级方法 (CentOS)
sudo yum update jq
```

### 问题 4: 语法错误

```bash
# 错误信息: parse error: Invalid numeric literal
# 常见原因: JSON格式错误或jq语法错误

# 调试方法:
echo 'your_json' | python3 -m json.tool  # 验证JSON格式
jq -n 'your_jq_expression'                # 测试jq表达式
```

## 📊 版本兼容性

### jq 版本差异

| 版本  | 发布时间 | 主要特性             | 兼容性 |
| ----- | -------- | -------------------- | ------ |
| 1.7+  | 2023+    | 改进错误信息、新函数 | 推荐   |
| 1.6   | 2018     | 稳定版本、广泛支持   | 良好   |
| 1.5   | 2015     | 基础功能完整         | 基本   |
| < 1.5 | 2015 前  | 功能有限             | 不推荐 |

### 语法兼容性检查

```bash
# 检查特定功能是否支持
jq -n 'try (1 | debug) catch "debug不支持"'
jq -n 'try (1 | halt_error(1)) catch "halt_error不支持"'
jq -n 'try ("test" | ascii_downcase) catch "ascii_downcase不支持"'
```

## 🔧 高级配置

### 环境变量配置

```bash
# 设置默认选项
export JQ_COLORS="0;90:0;37:0;37:0;37:0;32:1;37:1;37"

# 设置模块路径
export JQ_LIBRARY_PATH="/usr/local/lib/jq:/home/<USER>/.jq"
```

### 性能优化

```bash
# 编译时优化选项
./configure --enable-maintainer-mode --enable-debug=no --enable-optimization=yes

# 运行时内存限制
ulimit -v 1048576  # 限制虚拟内存为1GB
```

## 📋 安装检查清单

- [ ] jq 已安装且版本 >= 1.6
- [ ] `jq --version` 命令正常执行
- [ ] 基础 JSON 解析功能正常
- [ ] 复杂查询功能正常
- [ ] 错误处理功能正常
- [ ] 文件读取权限正常
- [ ] 环境变量配置正确

---

💡 **提示**: 推荐使用包管理器安装 jq，这样可以自动处理依赖关系和系统集成。如果遇到问题，优先检查版本兼容性和权限设置。
