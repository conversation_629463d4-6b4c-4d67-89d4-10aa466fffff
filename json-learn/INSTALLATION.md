# jq 安装部署指南

## 🚀 快速安装

### macOS
```bash
# 使用 Homebrew (推荐)
brew install jq

# 使用 MacPorts
sudo port install jq

# 验证安装
jq --version
```

### Ubuntu/Debian
```bash
# 使用 apt (推荐)
sudo apt update
sudo apt install jq

# 从官方仓库安装最新版本
sudo apt install software-properties-common
sudo add-apt-repository ppa:rmescandon/yq
sudo apt update
sudo apt install jq

# 验证安装
jq --version
```

### CentOS/RHEL/Fedora
```bash
# CentOS 7/8, RHEL 7/8
sudo yum install epel-release
sudo yum install jq

# Fedora
sudo dnf install jq

# 验证安装
jq --version
```

### Windows
```bash
# 使用 Chocolatey (推荐)
choco install jq

# 使用 Scoop
scoop install jq

# 使用 winget
winget install jqlang.jq

# 验证安装 (PowerShell/CMD)
jq --version
```

### Alpine Linux
```bash
# 使用 apk
apk add jq

# 验证安装
jq --version
```

## 📦 其他安装方式

### 二进制文件直接下载
```bash
# Linux x86_64
wget https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64
chmod +x jq-linux-amd64
sudo mv jq-linux-amd64 /usr/local/bin/jq

# macOS
wget https://github.com/jqlang/jq/releases/latest/download/jq-macos-amd64
chmod +x jq-macos-amd64
sudo mv jq-macos-amd64 /usr/local/bin/jq

# 验证安装
jq --version
```

### 源码编译安装
```bash
# 安装依赖
# Ubuntu/Debian
sudo apt-get install autoconf automake libtool make git

# CentOS/RHEL
sudo yum install autoconf automake libtool make git

# 下载源码
git clone https://github.com/jqlang/jq.git
cd jq

# 编译安装
git submodule update --init
autoreconf -i
./configure --with-oniguruma=builtin
make -j8
sudo make install

# 验证安装
jq --version
```

## 🐳 Docker 环境

### 使用官方 Docker 镜像
```bash
# 拉取镜像
docker pull ghcr.io/jqlang/jq:latest

# 使用示例 - 处理本地文件
docker run --rm -i ghcr.io/jqlang/jq:latest < data.json '.field'

# 挂载目录处理文件
docker run --rm -v "$PWD:$PWD" -w "$PWD" ghcr.io/jqlang/jq:latest '.field' data.json

# 创建别名方便使用
alias jq='docker run --rm -i -v "$PWD:$PWD" -w "$PWD" ghcr.io/jqlang/jq:latest'
```

### 在 Dockerfile 中安装
```dockerfile
# Alpine 基础镜像
FROM alpine:latest
RUN apk add --no-cache jq

# Ubuntu 基础镜像
FROM ubuntu:20.04
RUN apt-get update && apt-get install -y jq && rm -rf /var/lib/apt/lists/*

# 使用官方 jq 镜像作为基础
FROM ghcr.io/jqlang/jq:latest
```

## 🔍 环境验证

### 基础验证
```bash
# 检查版本
jq --version
# 期望输出: jq-1.7 或更高版本

# 检查帮助信息
jq --help

# 简单功能测试
echo '{"name": "test", "value": 42}' | jq '.name'
# 期望输出: "test"
```

### 完整功能测试
```bash
# 创建测试脚本
cat > test_jq.sh << 'EOF'
#!/bin/bash
echo "=== jq 功能测试 ==="

# 测试1: 基础JSON解析
echo "测试1: 基础解析"
echo '{"name": "张三", "age": 30}' | jq '.name' || echo "❌ 基础解析失败"

# 测试2: 数组操作
echo "测试2: 数组操作"
echo '[1,2,3,4,5]' | jq 'map(. * 2)' || echo "❌ 数组操作失败"

# 测试3: 复杂查询
echo "测试3: 复杂查询"
echo '{"users":[{"name":"张三","age":30},{"name":"李四","age":25}]}' | jq '.users[] | select(.age > 28)' || echo "❌ 复杂查询失败"

# 测试4: 错误处理
echo "测试4: 错误处理"
echo '{"data": null}' | jq '.data // "默认值"' || echo "❌ 错误处理失败"

echo "✅ 所有测试完成"
EOF

chmod +x test_jq.sh
./test_jq.sh
```

## ⚠️ 常见问题排查

### 问题1: 命令未找到
```bash
# 错误信息: command not found: jq
# 解决方案:
which jq                    # 检查是否在PATH中
echo $PATH                  # 检查PATH环境变量
sudo find / -name jq 2>/dev/null  # 查找jq文件位置

# 如果找到但不在PATH中，添加到PATH
export PATH=$PATH:/path/to/jq/directory
```

### 问题2: 权限问题
```bash
# 错误信息: Permission denied
# 解决方案:
ls -la $(which jq)          # 检查文件权限
sudo chmod +x $(which jq)  # 添加执行权限
```

### 问题3: 版本过旧
```bash
# 检查当前版本
jq --version

# 升级方法 (macOS)
brew upgrade jq

# 升级方法 (Ubuntu)
sudo apt update && sudo apt upgrade jq

# 升级方法 (CentOS)
sudo yum update jq
```

### 问题4: 语法错误
```bash
# 错误信息: parse error: Invalid numeric literal
# 常见原因: JSON格式错误或jq语法错误

# 调试方法:
echo 'your_json' | python3 -m json.tool  # 验证JSON格式
jq -n 'your_jq_expression'                # 测试jq表达式
```

## 📊 版本兼容性

### jq 版本差异
| 版本 | 发布时间 | 主要特性 | 兼容性 |
|------|----------|----------|--------|
| 1.7+ | 2023+ | 改进错误信息、新函数 | 推荐 |
| 1.6 | 2018 | 稳定版本、广泛支持 | 良好 |
| 1.5 | 2015 | 基础功能完整 | 基本 |
| < 1.5 | 2015前 | 功能有限 | 不推荐 |

### 语法兼容性检查
```bash
# 检查特定功能是否支持
jq -n 'try (1 | debug) catch "debug不支持"'
jq -n 'try (1 | halt_error(1)) catch "halt_error不支持"'
jq -n 'try ("test" | ascii_downcase) catch "ascii_downcase不支持"'
```

## 🔧 高级配置

### 环境变量配置
```bash
# 设置默认选项
export JQ_COLORS="0;90:0;37:0;37:0;37:0;32:1;37:1;37"

# 设置模块路径
export JQ_LIBRARY_PATH="/usr/local/lib/jq:/home/<USER>/.jq"
```

### 性能优化
```bash
# 编译时优化选项
./configure --enable-maintainer-mode --enable-debug=no --enable-optimization=yes

# 运行时内存限制
ulimit -v 1048576  # 限制虚拟内存为1GB
```

## 📋 安装检查清单

- [ ] jq 已安装且版本 >= 1.6
- [ ] `jq --version` 命令正常执行
- [ ] 基础JSON解析功能正常
- [ ] 复杂查询功能正常
- [ ] 错误处理功能正常
- [ ] 文件读取权限正常
- [ ] 环境变量配置正确

---
💡 **提示**: 推荐使用包管理器安装jq，这样可以自动处理依赖关系和系统集成。如果遇到问题，优先检查版本兼容性和权限设置。
