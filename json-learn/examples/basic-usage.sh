#!/bin/bash

# jq基础用法示例
# 运行前确保安装了jq: brew install jq (macOS) 或 apt-get install jq (Ubuntu)

DATA_FILE="data/sample.json"

echo "=== jq基础用法示例 ==="
echo

# 1. 基础字段访问
echo "1. 基础字段访问"
echo "获取第一个用户的姓名："
jq '.users[0].name' $DATA_FILE

echo "获取所有用户的邮箱："
jq '.users[].email' $DATA_FILE
echo

# 2. 数组操作
echo "2. 数组操作"
echo "用户总数："
jq '.users | length' $DATA_FILE

echo "获取最后一个用户："
jq '.users[-1].name' $DATA_FILE
echo

# 3. 条件过滤
echo "3. 条件过滤"
echo "活跃用户："
jq '.users[] | select(.active == true) | .name' $DATA_FILE

echo "年龄大于30的用户："
jq '.users[] | select(.age > 30) | {name, age}' $DATA_FILE
echo

# 4. 数据转换
echo "4. 数据转换"
echo "用户摘要："
jq '.users[] | {name, city: .address.city, skill_count: (.skills | length)}' $DATA_FILE
echo

# 5. 聚合操作
echo "5. 聚合操作"
echo "平均年龄："
jq '[.users[].age] | add / length' $DATA_FILE

echo "所有技能（去重）："
jq '[.users[].skills[]] | unique' $DATA_FILE
echo

# 6. 输出格式控制
echo "6. 输出格式控制"
echo "紧凑输出："
jq -c '.users[0]' $DATA_FILE

echo "原始字符串输出："
jq -r '.users[].name' $DATA_FILE
echo

# 7. 错误处理
echo "7. 安全访问"
echo "访问可能不存在的字段："
jq '.users[0].nonexistent // "字段不存在"' $DATA_FILE

echo "安全访问嵌套字段："
jq '.users[0].address?.zipcode // "邮编未设置"' $DATA_FILE
echo

echo "✅ 基础用法示例完成！"
echo "💡 提示：尝试修改上面的命令来探索更多功能"
