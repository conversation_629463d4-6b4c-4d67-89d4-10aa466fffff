#!/bin/bash

# 常见JSON处理模式示例

DATA_FILE="data/sample.json"

# 简单检查
if ! command -v jq &>/dev/null; then
  echo "❌ jq 未安装！请参考 INSTALLATION.md 安装jq"
  exit 1
fi

echo "=== 常见JSON处理模式 ==="
echo

# 1. 数据查询模式
echo "1. 🔍 数据查询模式"
echo "查找特定ID的用户："
jq '.users[] | select(.id == 2)' $DATA_FILE

echo "查找包含特定技能的用户："
jq '.users[] | select(.skills[] == "Python") | .name' $DATA_FILE

echo "查找来自特定城市的用户："
jq '.users[] | select(.address.city == "北京")' $DATA_FILE
echo

# 2. 数据统计模式
echo "2. 📊 数据统计模式"
echo "按城市统计用户数："
jq '.users | group_by(.address.city) | map({city: .[0].address.city, count: length})' $DATA_FILE

echo "统计活跃用户数："
jq '.users | map(select(.active)) | length' $DATA_FILE

echo "计算已完成项目的平均分数："
jq '[.users[].projects[] | select(.status == "completed") | .score] | add / length' $DATA_FILE
echo

# 3. 数据转换模式
echo "3. 🔧 数据转换模式"
echo "创建用户报告："
jq '.users | map({
  name,
  contact: .email,
  location: .address.city,
  skills: (.skills | length),
  status: (if .active then "活跃" else "非活跃" end)
})' $DATA_FILE

echo "提取项目列表："
jq '[.users[].projects[] | {user: .name, project: .name, status}] | sort_by(.status)' $DATA_FILE 2>/dev/null ||
  jq '[.users[] as $user | $user.projects[] | {user: $user.name, project: .name, status}] | sort_by(.status)' $DATA_FILE
echo

# 4. 数据修改模式
echo "4. ✏️ 数据修改模式"
echo "为用户添加等级标签："
jq '.users[] | . + {
  level: (if .age > 30 then "高级" elif .age > 25 then "中级" else "初级" end)
}' $DATA_FILE

echo "删除敏感信息："
jq '.users[] | del(.email)' $DATA_FILE
echo

# 5. 复杂查询模式
echo "5. 🎯 复杂查询模式"
echo "多条件筛选："
jq '.users[] | select(.active == true and .age > 25 and (.skills | length) >= 3)' $DATA_FILE

echo "嵌套数据处理："
jq '.users[] | {
  name,
  city: .address.city,
  project_summary: {
    total: (.projects | length),
    completed: (.projects | map(select(.status == "completed")) | length),
    avg_score: ([.projects[].score | select(. != null)] | if length > 0 then add / length else 0 end)
  }
}' $DATA_FILE
echo

# 6. 实用工具模式
echo "6. 🛠️ 实用工具模式"
echo "数据验证 - 检查必需字段："
jq '.users[] | select(has("name") and has("email") and has("age"))' $DATA_FILE >/dev/null && echo "✅ 所有用户都有必需字段"

echo "生成CSV格式："
echo "姓名,年龄,城市,技能数"
jq -r '.users[] | [.name, .age, .address.city, (.skills | length)] | @csv' $DATA_FILE

echo "创建索引映射："
jq '.users | map({(.name): .id}) | add' $DATA_FILE
echo

# 7. 15分钟挑战答案
echo "7. 🎯 15分钟挑战答案"
echo "任务1 - 提取所有用户名："
jq -r '.users[].name' $DATA_FILE

echo "任务2 - 年龄大于25的活跃用户："
jq '.users[] | select(.age > 25 and .active == true) | {name, age}' $DATA_FILE

echo "任务3 - 每个城市的用户数量："
jq '.users | group_by(.address.city) | map({city: .[0].address.city, count: length})' $DATA_FILE

echo "任务4 - 用户摘要报告："
jq '.users[] | {name, city: .address.city, skill_count: (.skills | length)}' $DATA_FILE
echo

echo "🎉 常见模式示例完成！"
echo "💡 这些模式覆盖了90%的日常JSON处理需求"
