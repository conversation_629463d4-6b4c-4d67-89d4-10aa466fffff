# 使用指南

## 📁 目录结构

```
json-learn/
├── README.md              # 🚀 快速入门指南 (15-30分钟掌握)
├── INSTALLATION.md        # 📦 完整安装部署指南
├── USAGE.md               # 📖 使用说明
├── check-environment.sh   # 🔍 环境诊断脚本
├── comparison.md           # 📊 工具详细对比
├── data/
│   └── sample.json        # 📄 示例数据文件
├── examples/
│   ├── basic-usage.sh     # 🔧 基础用法示例
│   └── common-patterns.sh # 🎯 常见模式示例
└── reference/
    ├── best-practices.md  # 💡 最佳实践要点
    └── jq-cheatsheet.md   # 📋 jq语法速查表
```

## 🎯 学习路径

### 第 0 步：环境准备 (5 分钟)

```bash
# 运行环境诊断 (推荐)
./check-environment.sh

# 或手动检查
# 查看安装指南
cat INSTALLATION.md

# 安装jq (选择适合你系统的命令)
brew install jq          # macOS
sudo apt install jq      # Ubuntu
sudo yum install jq      # CentOS

# 验证安装
jq --version
```

### 第 1 步：快速入门 (15 分钟)

```bash
# 阅读快速入门指南
cat README.md

# 运行基础示例
./examples/basic-usage.sh
```

### 第 2 步：实践练习 (15 分钟)

```bash
# 运行常见模式示例
./examples/common-patterns.sh

# 尝试15分钟挑战任务
# (任务在README.md中)
```

### 第 3 步：深入学习 (按需)

```bash
# 查看完整安装指南
cat INSTALLATION.md

# 查看语法速查表
cat reference/jq-cheatsheet.md

# 学习最佳实践
cat reference/best-practices.md

# 了解工具对比
cat comparison.md
```

## 🚀 快速测试

### 环境诊断 (推荐)

```bash
# 运行完整的环境诊断
./check-environment.sh
```

### 手动验证 jq 安装

```bash
jq --version
# 如果未安装：
# macOS: brew install jq
# Ubuntu: sudo apt-get install jq
```

### 运行示例

```bash
# 基础用法
./examples/basic-usage.sh

# 常见模式
./examples/common-patterns.sh

# 自定义测试
jq '.users[].name' data/sample.json
```

## 💡 核心要点

### 必须掌握的语法

- `.field` - 字段访问
- `.array[]` - 数组遍历
- `select()` - 条件过滤
- `map()` - 数组转换
- `|` - 管道操作

### 常用选项

- `-r` - 原始输出
- `-c` - 紧凑输出
- `-n` - 无输入模式

### 安全实践

- 使用 `//` 提供默认值
- 使用 `try-catch` 处理错误
- 参数化输入避免注入

## 🎯 15 分钟挑战

完成以下任务来检验学习效果：

1. **提取数据**：获取所有用户名
2. **条件过滤**：找出年龄>25 的活跃用户
3. **数据统计**：计算每个城市的用户数
4. **格式转换**：生成用户摘要报告

**答案在 examples/目录中！**

---

🎉 **开始学习吧！** 从 README.md 开始，30 分钟内掌握 Shell JSON 处理核心技能！
