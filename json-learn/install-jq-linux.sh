#!/bin/bash

# jq Linux生产环境自动安装脚本
# 支持 Ubuntu/Debian, CentOS/RHEL, Amazon Linux

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    else
        log_error "无法检测操作系统"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VERSION"
}

# 检查是否已安装jq
check_existing_jq() {
    if command -v jq &> /dev/null; then
        local version=$(jq --version)
        log_warning "jq已安装: $version"
        read -p "是否要重新安装? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "保持现有安装"
            exit 0
        fi
    fi
}

# Ubuntu/Debian安装
install_ubuntu() {
    log_info "在Ubuntu/Debian系统上安装jq..."
    
    # 更新包索引
    sudo apt update
    
    # 安装jq
    sudo apt install -y jq
    
    log_success "jq安装完成"
}

# CentOS/RHEL安装
install_centos() {
    log_info "在CentOS/RHEL系统上安装jq..."
    
    # 检测包管理器
    if command -v dnf &> /dev/null; then
        # CentOS 8+, RHEL 8+
        sudo dnf install -y epel-release
        sudo dnf install -y jq
    elif command -v yum &> /dev/null; then
        # CentOS 7, RHEL 7
        sudo yum install -y epel-release
        sudo yum install -y jq
    else
        log_error "未找到包管理器 (yum/dnf)"
        exit 1
    fi
    
    log_success "jq安装完成"
}

# Amazon Linux安装
install_amazon() {
    log_info "在Amazon Linux系统上安装jq..."
    
    if command -v dnf &> /dev/null; then
        # Amazon Linux 2023
        sudo dnf install -y jq
    elif command -v yum &> /dev/null; then
        # Amazon Linux 2
        sudo yum install -y jq
    else
        log_error "未找到包管理器"
        exit 1
    fi
    
    log_success "jq安装完成"
}

# 二进制安装 (备用方案)
install_binary() {
    log_info "使用二进制文件安装jq..."
    
    local temp_file="/tmp/jq-linux-amd64"
    local install_path="/usr/local/bin/jq"
    
    # 下载二进制文件
    if command -v wget &> /dev/null; then
        wget -O "$temp_file" https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64
    elif command -v curl &> /dev/null; then
        curl -L -o "$temp_file" https://github.com/jqlang/jq/releases/latest/download/jq-linux-amd64
    else
        log_error "需要wget或curl来下载文件"
        exit 1
    fi
    
    # 安装
    sudo mv "$temp_file" "$install_path"
    sudo chmod +x "$install_path"
    
    # 创建软链接
    sudo ln -sf "$install_path" /usr/bin/jq
    
    log_success "jq二进制安装完成"
}

# 验证安装
verify_installation() {
    log_info "验证jq安装..."
    
    if command -v jq &> /dev/null; then
        local version=$(jq --version)
        local path=$(which jq)
        log_success "jq安装成功!"
        log_info "版本: $version"
        log_info "路径: $path"
        
        # 功能测试
        if echo '{"test": "success"}' | jq -r '.test' &> /dev/null; then
            log_success "功能测试通过"
        else
            log_error "功能测试失败"
            exit 1
        fi
    else
        log_error "jq安装失败"
        exit 1
    fi
}

# 主安装函数
install_jq() {
    case "$OS" in
        ubuntu|debian)
            install_ubuntu
            ;;
        centos|rhel|rocky|almalinux)
            install_centos
            ;;
        amzn)
            install_amazon
            ;;
        *)
            log_warning "不支持的操作系统: $OS"
            log_info "尝试使用二进制安装..."
            install_binary
            ;;
    esac
}

# 主函数
main() {
    echo "=== jq Linux生产环境安装脚本 ==="
    echo
    
    # 检查权限
    if [[ $EUID -eq 0 ]]; then
        log_warning "不建议以root用户运行此脚本"
        read -p "继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # 检测系统
    detect_os
    
    # 检查现有安装
    check_existing_jq
    
    # 安装jq
    install_jq
    
    # 验证安装
    verify_installation
    
    echo
    log_success "jq安装完成! 现在可以开始使用JSON处理功能了"
    log_info "运行 './examples/basic-usage.sh' 开始学习"
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
