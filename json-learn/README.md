# Shell JSON 处理快速入门指南

> 🎯 **目标**：15-30 分钟内掌握 Shell 环境下 JSON 处理的核心技能

## 📚 学习路径

### 第零步：安装 jq (5 分钟)

> 📖 **详细安装指南**: 查看 `INSTALLATION.md`

**快速安装**:

```bash
# Ubuntu/Debian
sudo apt install jq

# CentOS/RHEL
sudo yum install jq

# 验证安装
jq --version
```

### 第一步：了解工具选择 (5 分钟)

| 工具        | 推荐度     | 适用场景       | 学习成本 |
| ----------- | ---------- | -------------- | -------- |
| **jq**      | ⭐⭐⭐⭐⭐ | 所有 JSON 处理 | 中等     |
| yq          | ⭐⭐⭐⭐   | 多格式数据     | 中等     |
| 传统工具    | ⭐⭐       | 简单文本匹配   | 低       |
| Python/Node | ⭐⭐⭐     | 复杂逻辑       | 高       |

**结论**：优先学习和使用 `jq`

### 第二步：掌握 jq 核心语法 (10 分钟)

#### 基础访问

```bash
# 字段访问
jq '.name' data.json              # 获取name字段
jq '.user.email' data.json        # 获取嵌套字段
jq '.users[0]' data.json          # 获取数组第一个元素
jq '.users[]' data.json           # 获取数组所有元素
```

#### 过滤和选择

```bash
# 条件过滤
jq '.users[] | select(.age > 30)' data.json
jq '.users[] | select(.active == true)' data.json

# 字符串匹配
jq '.users[] | select(.name | contains("张"))' data.json
```

#### 数据转换

```bash
# 提取特定字段
jq '.users[] | {name, age}' data.json

# 数组操作
jq '.users | map(.name)' data.json        # 提取所有姓名
jq '.users | length' data.json            # 数组长度
jq '.users | sort_by(.age)' data.json     # 按年龄排序
```

### 第三步：学会常用模式 (15 分钟)

#### 🔍 数据查询

```bash
# 查找特定用户
jq '.users[] | select(.id == 1)' data.json

# 统计活跃用户
jq '.users | map(select(.active)) | length' data.json

# 获取所有城市（去重）
jq '[.users[].address.city] | unique' data.json
```

#### 📊 数据聚合

```bash
# 计算平均年龄
jq '[.users[].age] | add / length' data.json

# 按城市分组统计
jq '.users | group_by(.address.city) | map({city: .[0].address.city, count: length})' data.json
```

#### 🔧 数据修改

```bash
# 添加字段
jq '.users[0] | . + {status: "VIP"}' data.json

# 删除字段
jq '.users[0] | del(.password)' data.json

# 条件修改
jq '.users[] | if .age > 30 then . + {level: "senior"} else . end' data.json
```

## 🚀 快速实践

### 1. 运行基础示例

```bash
cd json-learn
./examples/basic-usage.sh
```

### 2. 尝试常见模式

```bash
./examples/common-patterns.sh
```

### 3. 查看速查表

```bash
cat reference/jq-cheatsheet.md
```

## 📖 进阶学习

- `INSTALLATION.md` - 简洁安装指南
- `reference/jq-cheatsheet.md` - jq 语法速查表
- `reference/best-practices.md` - 最佳实践要点
- `comparison.md` - 工具对比

## 💡 核心要点

### ✅ 记住这些

1. **基础语法**：`.field`、`.array[]`、`select()`、`map()`
2. **常用选项**：`-r`(原始输出)、`-c`(紧凑)、`-n`(无输入)
3. **安全访问**：使用 `//` 提供默认值，`?` 安全访问
4. **错误处理**：使用 `try-catch` 处理异常

### ❌ 避免这些

1. 不要用传统工具处理复杂 JSON
2. 不要忽略 null 值处理
3. 不要在大文件上使用非流式处理
4. 不要直接拼接用户输入到 jq 表达式

## 🎯 15 分钟挑战

尝试完成以下任务来检验学习效果：

1. 从 `data/sample.json` 提取所有用户名
2. 找出年龄大于 25 的活跃用户
3. 计算每个城市的用户数量
4. 创建用户摘要报告（姓名、城市、技能数量）

**答案在 `examples/` 目录中！**

---

🎉 **恭喜！** 完成这个指南后，你已经掌握了 Shell 环境下 JSON 处理的核心技能。继续实践和探索更多高级功能吧！
