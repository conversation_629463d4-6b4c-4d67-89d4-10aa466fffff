#!/bin/bash

# jq环境诊断脚本
# 用于检查jq安装状态和功能完整性

echo "=== jq 环境诊断报告 ==="
echo "生成时间: $(date)"
echo

# 1. 基础检查
echo "1. 🔍 基础环境检查"
echo "操作系统: $(uname -s) $(uname -r)"
echo "Shell: $SHELL"
echo

# 2. jq安装检查
echo "2. 📦 jq安装状态"
if command -v jq &>/dev/null; then
    echo "✅ jq 已安装"
    echo "   版本: $(jq --version)"
    echo "   路径: $(which jq)"
    echo "   权限: $(ls -la $(which jq) | awk '{print $1, $3, $4}')"
else
    echo "❌ jq 未安装"
    echo "   建议安装命令:"
    case "$(uname -s)" in
    Darwin)
        echo "   macOS: brew install jq"
        ;;
    Linux)
        if command -v apt &>/dev/null; then
            echo "   Ubuntu/Debian: sudo apt install jq"
        elif command -v yum &>/dev/null; then
            echo "   CentOS/RHEL: sudo yum install jq"
        elif command -v dnf &>/dev/null; then
            echo "   Fedora: sudo dnf install jq"
        else
            echo "   Linux: 请参考 INSTALLATION.md"
        fi
        ;;
    *)
        echo "   请参考 INSTALLATION.md"
        ;;
    esac
    exit 1
fi
echo

# 3. 版本兼容性检查
echo "3. 🔄 版本兼容性检查"
version=$(jq --version | grep -o '[0-9]\+\.[0-9]\+')
major=$(echo $version | cut -d. -f1)
minor=$(echo $version | cut -d. -f2)

if [[ $major -gt 1 ]] || [[ $major -eq 1 && $minor -ge 6 ]]; then
    echo "✅ 版本兼容性良好 (>= 1.6)"
elif [[ $major -eq 1 && $minor -ge 5 ]]; then
    echo "⚠️  版本较旧但可用 (1.5.x)"
    echo "   建议升级到1.6+以获得更好的功能支持"
else
    echo "❌ 版本过旧 (< 1.5)"
    echo "   强烈建议升级到1.6+版本"
fi
echo

# 4. 功能测试
echo "4. 🧪 功能完整性测试"

# 测试1: 基础JSON解析
echo -n "   基础解析: "
if echo '{"test": "value"}' | jq -r '.test' &>/dev/null; then
    echo "✅"
else
    echo "❌"
fi

# 测试2: 数组操作
echo -n "   数组操作: "
if echo '[1,2,3]' | jq 'map(. * 2)' &>/dev/null; then
    echo "✅"
else
    echo "❌"
fi

# 测试3: 复杂查询
echo -n "   复杂查询: "
if echo '{"users":[{"name":"test","age":30}]}' | jq '.users[] | select(.age > 25)' &>/dev/null; then
    echo "✅"
else
    echo "❌"
fi

# 测试4: 错误处理
echo -n "   错误处理: "
if echo '{"data": null}' | jq '.data // "default"' &>/dev/null; then
    echo "✅"
else
    echo "❌"
fi

# 测试5: 高级功能
echo -n "   高级功能: "
if jq -n 'try (1 | debug) catch "not supported"' &>/dev/null; then
    echo "✅"
else
    echo "⚠️  部分高级功能不支持"
fi
echo

# 5. 依赖检查
echo "5. 🔗 系统依赖检查"
echo -n "   libonig (正则表达式): "
if ldd $(which jq) 2>/dev/null | grep -q onig; then
    echo "✅ 已链接"
elif otool -L $(which jq) 2>/dev/null | grep -q onig; then
    echo "✅ 已链接 (macOS)"
else
    echo "⚠️  未检测到或静态链接"
fi

echo -n "   libc: "
if ldd $(which jq) 2>/dev/null | grep -q libc; then
    echo "✅ 已链接"
elif otool -L $(which jq) 2>/dev/null | grep -q libc; then
    echo "✅ 已链接 (macOS)"
else
    echo "⚠️  未检测到或静态链接"
fi
echo

# 6. 示例数据检查
echo "6. 📄 示例数据检查"
if [[ -f "data/sample.json" ]]; then
    echo "✅ 示例数据文件存在"
    if jq empty data/sample.json 2>/dev/null; then
        echo "✅ 示例数据格式正确"
    else
        echo "❌ 示例数据格式错误"
    fi
else
    echo "❌ 示例数据文件不存在"
    echo "   请确保在json-learn目录中运行此脚本"
fi
echo

# 7. 性能基准测试
echo "7. ⚡ 性能基准测试"
echo -n "   小数据处理: "
if command -v gdate &>/dev/null; then
    # macOS with GNU coreutils
    start_time=$(gdate +%s%N)
    for i in {1..100}; do
        echo '{"test": "value"}' | jq '.test' >/dev/null
    done
    end_time=$(gdate +%s%N)
    duration=$(((end_time - start_time) / 1000000))
    echo "${duration}ms (100次操作)"
else
    # 使用秒级精度
    start_time=$(date +%s)
    for i in {1..100}; do
        echo '{"test": "value"}' | jq '.test' >/dev/null
    done
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    echo "${duration}s (100次操作)"
fi

if [[ -f "data/sample.json" ]]; then
    echo -n "   示例数据处理: "
    if command -v gdate &>/dev/null; then
        start_time=$(gdate +%s%N)
        jq '.users[] | {name, age}' data/sample.json >/dev/null
        end_time=$(gdate +%s%N)
        duration=$(((end_time - start_time) / 1000000))
        echo "${duration}ms"
    else
        start_time=$(date +%s)
        jq '.users[] | {name, age}' data/sample.json >/dev/null
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo "${duration}s"
    fi
fi
echo

# 8. 总结和建议
echo "8. 📋 总结和建议"
if command -v jq &>/dev/null; then
    if [[ $major -gt 1 ]] || [[ $major -eq 1 && $minor -ge 6 ]]; then
        echo "✅ 环境配置良好，可以开始学习jq"
        echo "   建议运行: ./examples/basic-usage.sh"
    else
        echo "⚠️  建议升级jq版本以获得更好的体验"
        echo "   升级后运行: ./examples/basic-usage.sh"
    fi
else
    echo "❌ 需要先安装jq"
    echo "   安装完成后重新运行此脚本"
fi

echo
echo "=== 诊断完成 ==="
echo "如有问题，请参考 INSTALLATION.md 获取详细安装指南"
