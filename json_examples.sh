#!/bin/bash

# JSON处理示例脚本
# 使用sample_data.json作为测试数据

echo "=== JSON处理示例 ==="
echo

# 1. 基本字段提取
echo "1. 提取特定字段的值："
echo "提取所有用户名："
jq '.users[].name' sample_data.json
echo

echo "提取第一个用户的邮箱："
jq '.users[0].email' sample_data.json
echo

echo "提取元数据中的总数："
jq '.metadata.total_count' sample_data.json
echo

# 2. 数组操作
echo "2. 数组操作："
echo "获取所有用户ID："
jq '.users[].id' sample_data.json
echo

echo "获取第一个和最后一个用户："
jq '.users[0], .users[-1]' sample_data.json
echo

echo "获取数组长度："
jq '.users | length' sample_data.json
echo

# 3. 过滤和筛选
echo "3. 过滤和筛选："
echo "筛选活跃用户："
jq '.users[] | select(.active == true)' sample_data.json
echo

echo "筛选年龄大于30的用户："
jq '.users[] | select(.age > 30)' sample_data.json
echo

echo "筛选来自北京的用户："
jq '.users[] | select(.address.city == "北京")' sample_data.json
echo

# 4. 数据转换
echo "4. 数据转换："
echo "创建用户摘要："
jq '.users[] | {name: .name, city: .address.city, skill_count: (.skills | length)}' sample_data.json
echo

echo "提取所有技能（去重）："
jq '[.users[].skills[]] | unique' sample_data.json
echo

# 5. 聚合操作
echo "5. 聚合操作："
echo "计算平均年龄："
jq '[.users[].age] | add / length' sample_data.json
echo

echo "统计各城市用户数："
jq 'group_by(.users[].address.city) | map({city: .[0].users[0].address.city, count: length})' sample_data.json 2>/dev/null || echo "使用更简单的方法："
jq '[.users[].address.city] | group_by(.) | map({city: .[0], count: length})' sample_data.json
echo

# 6. 嵌套数据处理
echo "6. 嵌套数据处理："
echo "获取所有项目名称："
jq '.users[].projects[].name' sample_data.json
echo

echo "获取已完成项目的平均分数："
jq '[.users[].projects[] | select(.status == "completed") | .score] | add / length' sample_data.json
echo

# 7. 条件处理
echo "7. 条件处理："
echo "为每个用户添加状态标签："
jq '.users[] | . + {status_label: (if .active then "活跃" else "非活跃" end)}' sample_data.json
echo

# 8. 输出格式控制
echo "8. 输出格式控制："
echo "紧凑输出："
jq -c '.users[0]' sample_data.json
echo

echo "原始字符串输出："
jq -r '.users[].name' sample_data.json
echo

# 9. 修改JSON结构
echo "9. 修改JSON结构："
echo "添加新字段："
jq '.users[0] | . + {department: "技术部", level: "高级"}' sample_data.json
echo

echo "删除字段："
jq '.users[0] | del(.email)' sample_data.json
echo

# 10. 错误处理
echo "10. 错误处理："
echo "安全访问可能不存在的字段："
jq '.users[]?.nonexistent_field // "默认值"' sample_data.json
echo
