#!/bin/bash

# 使用传统Shell工具处理JSON的示例
# 注意：这些方法不如jq强大，但在某些简单场景下可用

echo "=== 使用传统工具处理JSON ==="
echo

# 1. 使用grep提取简单值
echo "1. 使用grep提取简单值："
echo "提取所有name字段（简单情况）："
grep -o '"name": "[^"]*"' sample_data.json
echo

echo "提取email地址："
grep -o '"email": "[^"]*"' sample_data.json | cut -d'"' -f4
echo

# 2. 使用sed进行简单替换
echo "2. 使用sed进行简单文本替换："
echo "将所有'active': true改为'active': false："
sed 's/"active": true/"active": false/g' sample_data.json | head -10
echo

# 3. 使用awk处理JSON行
echo "3. 使用awk处理JSON："
echo "提取包含age的行并显示："
awk '/"age":/ {print $0}' sample_data.json
echo

echo "计算所有age值的总和（简单方法）："
grep -o '"age": [0-9]*' sample_data.json | awk -F': ' '{sum += $2} END {print "总年龄:", sum}'
echo

# 4. 组合使用多个工具
echo "4. 组合使用多个工具："
echo "提取所有城市名称："
grep -o '"city": "[^"]*"' sample_data.json | cut -d'"' -f4 | sort | uniq
echo

# 5. 使用Python一行命令处理JSON
echo "5. 使用Python一行命令："
echo "提取所有用户名："
python3 -c "
import json, sys
data = json.load(open('sample_data.json'))
for user in data['users']:
    print(user['name'])
"
echo

echo "计算平均年龄："
python3 -c "
import json
data = json.load(open('sample_data.json'))
ages = [user['age'] for user in data['users']]
print(f'平均年龄: {sum(ages)/len(ages):.1f}')
"
echo

# 6. 使用Node.js一行命令
echo "6. 使用Node.js一行命令（如果安装了node）："
if command -v node &> /dev/null; then
    echo "提取所有技能："
    node -e "
    const fs = require('fs');
    const data = JSON.parse(fs.readFileSync('sample_data.json'));
    const skills = data.users.flatMap(user => user.skills);
    console.log([...new Set(skills)].join(', '));
    "
else
    echo "Node.js未安装，跳过此示例"
fi
echo

# 7. 传统工具的局限性演示
echo "7. 传统工具的局限性："
echo "尝试用grep提取嵌套的address.city（会有问题）："
grep -o '"city": "[^"]*"' sample_data.json | head -3
echo "注意：这种方法无法处理复杂的嵌套结构和数组索引"
echo

# 8. 简单的JSON验证
echo "8. 简单的JSON格式验证："
echo "检查JSON文件是否有基本的语法错误："
if python3 -c "import json; json.load(open('sample_data.json'))" 2>/dev/null; then
    echo "JSON格式正确"
else
    echo "JSON格式有误"
fi
echo

# 9. 统计JSON文件信息
echo "9. JSON文件统计信息："
echo "文件大小："
ls -lh sample_data.json | awk '{print $5}'
echo "行数："
wc -l < sample_data.json
echo "字符数："
wc -c < sample_data.json
echo

echo "=== 总结 ==="
echo "传统工具的优点："
echo "- 系统自带，无需安装额外工具"
echo "- 对于简单的文本匹配和替换很有效"
echo "- 可以与其他Shell工具很好地组合"
echo
echo "传统工具的缺点："
echo "- 无法正确处理JSON的嵌套结构"
echo "- 不能处理转义字符"
echo "- 容易出错，特别是在复杂JSON结构中"
echo "- 无法进行类型感知的操作"
echo
echo "建议：对于JSON处理，优先使用jq等专门工具！"
