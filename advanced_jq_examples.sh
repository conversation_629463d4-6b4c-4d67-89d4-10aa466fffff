#!/bin/bash

# 高级jq用法示例

echo "=== 高级jq用法示例 ==="
echo

# 1. 复杂的数据重构
echo "1. 复杂的数据重构："
echo "将用户数据重构为以城市分组的格式："
jq '
{
  cities: (
    .users 
    | group_by(.address.city) 
    | map({
        city: .[0].address.city,
        users: map({name, age, active}),
        user_count: length,
        avg_age: (map(.age) | add / length)
      })
  ),
  summary: {
    total_users: (.users | length),
    active_users: (.users | map(select(.active)) | length),
    cities_count: (.users | map(.address.city) | unique | length)
  }
}
' sample_data.json
echo

# 2. 递归处理
echo "2. 递归处理 - 查找所有字符串值："
jq '
def find_strings: 
  if type == "object" then
    [.[] | find_strings] | flatten
  elif type == "array" then
    [.[] | find_strings] | flatten  
  elif type == "string" then
    [.]
  else
    []
  end;

find_strings | unique | sort
' sample_data.json
echo

# 3. 条件聚合
echo "3. 条件聚合 - 按状态统计项目："
jq '
.users[].projects[] 
| group_by(.status) 
| map({
    status: .[0].status,
    count: length,
    avg_score: (map(.score // 0) | add / length),
    projects: map(.name)
  })
' sample_data.json
echo

# 4. 数据验证
echo "4. 数据验证 - 检查数据完整性："
jq '
.users | map(
  {
    name: .name,
    issues: [
      (if .email | test("@") | not then "无效邮箱" else empty end),
      (if .age < 18 or .age > 65 then "年龄超出范围" else empty end),
      (if .skills | length == 0 then "技能为空" else empty end),
      (if .address.zipcode | length != 6 then "邮编格式错误" else empty end)
    ]
  }
) | map(select(.issues | length > 0))
' sample_data.json
echo

# 5. 动态字段访问
echo "5. 动态字段访问："
echo "根据变量访问字段："
field="name"
jq --arg field "$field" '.users[] | .[$field]' sample_data.json
echo

# 6. 多文件处理
echo "6. 多文件处理示例："
# 创建第二个JSON文件
cat > sample_data2.json << 'EOF'
{
  "departments": [
    {"id": 1, "name": "技术部", "budget": 1000000},
    {"id": 2, "name": "市场部", "budget": 800000}
  ]
}
EOF

echo "合并两个JSON文件："
jq -s '
{
  users: .[0].users,
  departments: .[1].departments,
  combined_info: {
    total_users: (.[0].users | length),
    total_departments: (.[1].departments | length),
    total_budget: (.[1].departments | map(.budget) | add)
  }
}
' sample_data.json sample_data2.json
echo

# 7. 流式处理大文件
echo "7. 流式处理（适用于大文件）："
echo "逐个处理用户记录："
jq -c '.users[]' sample_data.json | while read -r user; do
    name=$(echo "$user" | jq -r '.name')
    age=$(echo "$user" | jq -r '.age')
    echo "处理用户: $name (年龄: $age)"
done
echo

# 8. 自定义函数
echo "8. 自定义函数："
jq '
def calculate_score(projects):
  projects | map(.score // 0) | add / length;

def user_summary(user):
  {
    name: user.name,
    location: "\(user.address.city)-\(user.address.district)",
    avg_project_score: calculate_score(user.projects),
    skill_level: (
      if (user.skills | length) >= 3 then "高级"
      elif (user.skills | length) >= 2 then "中级"  
      else "初级"
      end
    )
  };

.users | map(user_summary(.))
' sample_data.json
echo

# 9. 错误处理和调试
echo "9. 错误处理和调试："
echo "使用try-catch处理可能的错误："
jq '
.users[] | 
{
  name: .name,
  email_domain: (
    try (.email | split("@")[1])
    catch "无效邮箱"
  ),
  first_project_score: (
    try .projects[0].score
    catch "无项目数据"
  )
}
' sample_data.json
echo

# 10. 性能优化技巧
echo "10. 性能优化技巧："
echo "使用索引而不是重复搜索："
jq '
# 创建技能索引
(.users | map(.skills[]) | unique) as $all_skills |
.users | map(
  . + {
    skill_indices: [.skills[] | . as $skill | ($all_skills | index($skill))]
  }
)
' sample_data.json | jq '.[] | {name, skill_indices}' | head -10
echo

# 清理临时文件
rm -f sample_data2.json

echo "=== jq高级技巧总结 ==="
echo "1. 使用管道 | 连接操作"
echo "2. 善用 map(), select(), group_by() 等内置函数"
echo "3. 定义自定义函数提高代码复用性"
echo "4. 使用 try-catch 处理异常情况"
echo "5. 对于大文件，考虑流式处理"
echo "6. 使用 -s 选项处理多个文件"
echo "7. 合理使用变量和参数传递"
