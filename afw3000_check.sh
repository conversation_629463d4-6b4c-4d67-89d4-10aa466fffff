#!/bin/bash
# 版本: 1.0
# 作者: dkyzheng
# 描述: AFW3000防火墙设备检测统一入口

# =============================================================================
# 脚本初始化
# =============================================================================

# 设置脚本目录和工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 脚本信息
SCRIPT_NAME="AFW3000防火墙检测工具"
SCRIPT_VERSION="1.0"
SCRIPT_DATE=$(date +"%Y-%m-%d %H:%M:%S")

# 引入公共函数库
if [[ -f "lib/common_functions.sh" ]]; then
    source "lib/common_functions.sh"
else
    echo "错误: 无法找到公共函数库 lib/common_functions.sh"
    exit 1
fi

if [[ -f "lib/ssh_utils.sh" ]]; then
    source "lib/ssh_utils.sh"
else
    log_error "无法找到SSH工具库 lib/ssh_utils.sh"
    exit 1
fi

# =============================================================================
# 全局变量
# =============================================================================

# 检测目标节点
TARGET_NODES=""
ALL_NODES=("n1" "n2" "n3")

# 本地检测模式标志
LOCAL_MODE=false

# 报告相关
REPORT_DIR="report"
REPORT_FILE=""
START_TIME=""
RAW_DATA_DIR="/tmp/afw3000_raw_data_$$"
REPORT_TAG=""  # 报告标识符，默认为空

# =============================================================================
# 工具函数
# =============================================================================

# 验证报告标识符格式
validate_report_tag() {
    local tag="$1"

    # 允许空标识符（跳过验证）
    if [[ -z "$tag" ]]; then
        return 0
    fi

    # 检查长度（建议1-50字符）
    if [[ ${#tag} -gt 50 ]]; then
        log_error "标识符长度不能超过50个字符"
        return 1
    fi

    # 检查字符（只允许字母和数字）
    if [[ ! "$tag" =~ ^[a-zA-Z0-9]+$ ]]; then
        log_error "标识符只能包含字母和数字"
        return 1
    fi

    return 0
}

# =============================================================================
# 帮助信息和使用说明
# =============================================================================

show_help() {
    cat <<EOF

========================================
$SCRIPT_NAME v$SCRIPT_VERSION
========================================

用法: $0 [选项] [目标节点]

默认行为:
  无参数时自动执行本地检测（检测当前主机）

远程检测目标节点:
  all       检测所有节点（N1、N2、N3）
  n1        仅检测N1节点（主节点，默认安博通防火墙）
  n2        仅检测N2节点（协节点）
  n3        仅检测N3节点（协节点）

选项:
  -h, --help     显示此帮助信息
  -v, --version  显示版本信息
  --debug        启用调试模式
  -t, --tag TAG  添加自定义标识符到报告文件名（仅支持字母和数字，最长50字符）

使用示例:
  $0                        # 本地检测（默认行为）
  $0 --debug                # 本地检测+调试模式
  $0 all                    # 检测所有远程节点
  $0 n1                     # 仅检测N1节点
  $0 --tag production n1    # 检测N1节点，报告文件名包含"production"标识
  $0 -t test all            # 检测所有节点，报告文件名包含"test"标识
  $0 --debug --tag backup n2  # 调试模式检测N2节点，包含"backup"标识

检测内容:
- 网络连通性检测
- 硬件信息检测（配置）
- 系统信息检测（配置，状态）
- 软件信息检测（配置，状态）

输出:
- 报告保存目录：report/
- 本地检测报告格式: afw3000_local_check_YYYYMMDD_HHMMSS.txt
- 远程检测报告格式: afw3000_check_YYYYMMDD_HHMMSS.txt

EOF
}

show_version() {
    cat <<EOF
版本: $SCRIPT_VERSION
EOF
}

# =============================================================================
# 参数解析和验证
# =============================================================================

parse_arguments() {
    local debug_mode=false

    # 如果没有参数，默认执行本地检测
    if [[ $# -eq 0 ]]; then
        LOCAL_MODE=true
        TARGET_NODES="localhost"
        log_info "默认执行本地检测模式"
        return 0
    fi

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
        -h | --help)
            show_help
            exit 0
            ;;
        -v | --version)
            show_version
            exit 0
            ;;
        --debug)
            debug_mode=true
            log_info "启用调试模式"
            shift
            ;;
        -t | --tag)
            if [[ -n "$2" && "$2" != -* ]]; then
                if validate_report_tag "$2"; then
                    REPORT_TAG="$2"
                    log_info "设置报告标识符: $REPORT_TAG"
                    shift 2
                else
                    exit 1
                fi
            else
                log_error "--tag 参数需要提供标识符值"
                exit 1
            fi
            ;;
        all)
            TARGET_NODES="n1 n2 n3"
            log_info "检测目标: 所有节点 (N1, N2, N3)"
            shift
            ;;
        n1 | n2 | n3)
            TARGET_NODES="$1"
            log_info "检测目标: $1节点"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            echo ""
            show_help
            exit 1
            ;;
        esac
    done

    # 如果没有设置目标节点，默认为本地检测
    if [[ -z "$TARGET_NODES" ]]; then
        LOCAL_MODE=true
        TARGET_NODES="localhost"
        log_info "默认执行本地检测模式"
    fi

    # 设置调试模式
    if [[ "$debug_mode" == true ]]; then
        set -x # 启用命令跟踪
    fi
}

# =============================================================================
# 初始化和环境检查
# =============================================================================

initialize_environment() {
    log_info "初始化检测环境..."

    # 记录开始时间
    START_TIME=$(date +"%Y%m%d_%H%M%S")

    # 创建报告目录
    if [[ ! -d "$REPORT_DIR" ]]; then
        mkdir -p "$REPORT_DIR"
        log_info "创建报告目录: $REPORT_DIR"
    fi

    # 本地模式下跳过设备配置加载和SSH依赖检查
    if [[ "$LOCAL_MODE" != "true" ]]; then
        # 加载配置文件
        if ! load_config "config/devices.conf"; then
            log_error "设备配置文件加载失败"
            exit 1
        fi

        if ! load_config "config/check_config.conf"; then
            log_error "检测配置文件加载失败"
            exit 1
        fi

        # 检查依赖工具
        if ! check_dependencies; then
            log_error "依赖工具检查失败"
            exit 1
        fi
    else
        log_info "本地检测模式，跳过远程配置加载和SSH依赖检查"
    fi

    log_info "环境初始化完成"
}

# =============================================================================
# 检测执行逻辑
# =============================================================================

# 本地检测执行函数
execute_local_detection() {
    log_info "开始本地检测..."

    # 获取本地主机信息
    local hostname=$(hostname)
    local local_ip=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}' 2>/dev/null || echo "127.0.0.1")

    log_info "本地主机信息: $hostname ($local_ip)"

    # 硬件检测模块（本地模式）
    log_info "执行硬件检测..."
    local hardware_check_result=""
    local hardware_check_output=""

    # 调用硬件检测模块并捕获输出
    hardware_check_output=$(./modules/hardware_check.sh localhost "$local_ip" "$(whoami)" "" 2>&1)
    local hardware_exit_code=$?

    # 将硬件检测结果保存到临时文件，供报告生成使用
    local temp_hardware_file="/tmp/afw3000_hardware_result_localhost_$$"
    echo "HARDWARE_CHECK_EXIT_CODE=$hardware_exit_code" >"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_START" >>"$temp_hardware_file"
    echo "$hardware_check_output" >>"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_END" >>"$temp_hardware_file"

    if [[ $hardware_exit_code -eq 0 ]]; then
        log_info "本地硬件检测成功"
        hardware_check_result="成功"
    else
        log_warn "本地硬件检测失败"
        hardware_check_result="失败"
    fi

    # 系统信息检测模块（本地模式）
    log_info "执行系统信息检测..."
    local system_check_result=""
    local system_check_output=""

    # 调用系统信息检测模块并捕获输出
    system_check_output=$(./modules/system_check.sh localhost "$local_ip" "$(whoami)" "" 22 2>&1)
    local system_exit_code=$?

    # 将系统信息检测结果保存到临时文件，供报告生成使用
    local temp_system_file="/tmp/afw3000_system_result_localhost_$$"
    echo "SYSTEM_CHECK_EXIT_CODE=$system_exit_code" >"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_START" >>"$temp_system_file"
    echo "$system_check_output" >>"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_END" >>"$temp_system_file"

    if [[ $system_exit_code -eq 0 ]]; then
        log_info "本地系统信息检测成功"
        system_check_result="成功"
    else
        log_warn "本地系统信息检测失败"
        system_check_result="失败"
    fi

    # 软件信息检测模块（本地模式）
    log_info "执行软件信息检测..."
    local software_check_result=""
    local software_check_output=""

    # 调用软件信息检测模块并捕获输出
    software_check_output=$(./modules/software_check.sh localhost "$local_ip" "$(whoami)" "" 22 2>&1)
    local software_exit_code=$?

    # 将软件信息检测结果保存到临时文件，供报告生成使用
    local temp_software_file="/tmp/afw3000_software_result_localhost_$$"
    echo "SOFTWARE_CHECK_EXIT_CODE=$software_exit_code" >"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_START" >>"$temp_software_file"
    echo "$software_check_output" >>"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_END" >>"$temp_software_file"

    if [[ $software_exit_code -eq 0 ]]; then
        log_info "本地软件信息检测成功"
        software_check_result="成功"
    else
        log_warn "本地软件信息检测失败"
        software_check_result="失败"
    fi

    log_info "本地检测完成 (硬件检测: $hardware_check_result, 系统信息检测: $system_check_result, 软件信息检测: $software_check_result)"

    return 0
}

execute_detection() {
    local target_node="$1"

    # 本地检测模式
    if [[ "$LOCAL_MODE" == "true" ]]; then
        execute_local_detection
        return $?
    fi

    log_info "开始检测 $target_node 节点..."

    # 获取节点配置
    local node_config
    node_config=$(get_device_config "$target_node")
    if [[ $? -ne 0 ]]; then
        log_error "$target_node 节点配置获取失败"
        return 1
    fi

    # 解析节点配置
    local host user password port backup_user backup_password
    read -r host user password port backup_user backup_password <<<"$node_config"

    log_info "$target_node 节点信息: $user@$host:$port"

    # 网络检测模块
    log_info "执行网络检测..."
    local network_check_result=""
    local network_check_output=""

    # 调用网络检测模块并捕获输出
    network_check_output=$(./modules/network_check.sh "$target_node" "$host" "$user" "$password" 2>&1)
    local network_exit_code=$?

    # 将网络检测结果保存到临时文件，供报告生成使用
    local temp_result_file="/tmp/afw3000_network_result_${target_node}_$$"
    echo "NETWORK_CHECK_EXIT_CODE=$network_exit_code" >"$temp_result_file"
    echo "NETWORK_CHECK_OUTPUT_START" >>"$temp_result_file"
    echo "$network_check_output" >>"$temp_result_file"
    echo "NETWORK_CHECK_OUTPUT_END" >>"$temp_result_file"

    if [[ $network_exit_code -eq 0 ]]; then
        log_info "$target_node 节点网络检测成功"
        network_check_result="成功"
    else
        log_error "$target_node 节点网络检测失败"
        network_check_result="失败"
        return 1
    fi

    # 硬件检测模块（在网络检测成功后执行）
    log_info "执行硬件检测..."
    local hardware_check_result=""
    local hardware_check_output=""

    # 调用硬件检测模块并捕获输出
    hardware_check_output=$(./modules/hardware_check.sh "$target_node" "$host" "$user" "$password" 2>&1)
    local hardware_exit_code=$?

    # 将硬件检测结果保存到临时文件，供报告生成使用
    local temp_hardware_file="/tmp/afw3000_hardware_result_${target_node}_$$"
    echo "HARDWARE_CHECK_EXIT_CODE=$hardware_exit_code" >"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_START" >>"$temp_hardware_file"
    echo "$hardware_check_output" >>"$temp_hardware_file"
    echo "HARDWARE_CHECK_OUTPUT_END" >>"$temp_hardware_file"

    if [[ $hardware_exit_code -eq 0 ]]; then
        log_info "$target_node 节点硬件检测成功"
        hardware_check_result="成功"
    else
        log_warn "$target_node 节点硬件检测失败"
        hardware_check_result="失败"
        # 硬件检测失败不影响整体流程，继续执行
    fi

    # 系统信息检测模块（在硬件检测之后执行）
    log_info "执行系统信息检测..."
    local system_check_result=""
    local system_check_output=""

    # 调用系统信息检测模块并捕获输出
    system_check_output=$(./modules/system_check.sh "$target_node" "$host" "$user" "$password" "$port" 2>&1)
    local system_exit_code=$?

    # 将系统信息检测结果保存到临时文件，供报告生成使用
    local temp_system_file="/tmp/afw3000_system_result_${target_node}_$$"
    echo "SYSTEM_CHECK_EXIT_CODE=$system_exit_code" >"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_START" >>"$temp_system_file"
    echo "$system_check_output" >>"$temp_system_file"
    echo "SYSTEM_CHECK_OUTPUT_END" >>"$temp_system_file"

    if [[ $system_exit_code -eq 0 ]]; then
        log_info "$target_node 节点系统信息检测成功"
        system_check_result="成功"
    else
        log_warn "$target_node 节点系统信息检测失败"
        system_check_result="失败"
        # 系统信息检测失败不影响整体流程，继续执行
    fi

    # 软件信息检测模块（在系统信息检测之后执行）
    log_info "执行软件信息检测..."
    local software_check_result=""
    local software_check_output=""

    # 调用软件信息检测模块并捕获输出
    software_check_output=$(./modules/software_check.sh "$target_node" "$host" "$user" "$password" "$port" 2>&1)
    local software_exit_code=$?

    # 将软件信息检测结果保存到临时文件，供报告生成使用
    local temp_software_file="/tmp/afw3000_software_result_${target_node}_$$"
    echo "SOFTWARE_CHECK_EXIT_CODE=$software_exit_code" >"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_START" >>"$temp_software_file"
    echo "$software_check_output" >>"$temp_software_file"
    echo "SOFTWARE_CHECK_OUTPUT_END" >>"$temp_software_file"

    if [[ $software_exit_code -eq 0 ]]; then
        log_info "$target_node 节点软件信息检测成功"
        software_check_result="成功"
    else
        log_warn "$target_node 节点软件信息检测失败"
        software_check_result="失败"
        # 软件信息检测失败不影响整体流程，继续执行
    fi

    log_info "$target_node 节点检测完成 (网络检测: $network_check_result, 硬件检测: $hardware_check_result, 系统信息检测: $system_check_result, 软件信息检测: $software_check_result)"

    return 0
}

run_detection() {
    log_info "开始执行检测任务..."

    local success_count=0
    local total_count=0

    # 遍历目标节点执行检测
    for node in $TARGET_NODES; do
        total_count=$((total_count + 1))

        echo ""
        log_info "========== 检测节点: $node =========="

        if execute_detection "$node"; then
            success_count=$((success_count + 1))
            log_info "$node 节点检测成功"
        else
            log_error "$node 节点检测失败"
        fi

        echo ""
    done

    # 输出检测结果统计
    log_info "========== 检测结果统计 =========="
    log_info "总节点数: $total_count"
    log_info "成功节点数: $success_count"
    log_info "失败节点数: $((total_count - success_count))"

    if [[ $success_count -eq $total_count ]]; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# 原始数据收集函数（调用原始数据收集模块）
# =============================================================================

# 调用原始数据收集模块
call_raw_data_collector() {
    local action="$1"
    shift

    if [[ -f "modules/raw_data_collector.sh" ]]; then
        bash "modules/raw_data_collector.sh" "$action" "$@"
    else
        log_error "原始数据收集模块不存在: modules/raw_data_collector.sh"
        return 1
    fi
}

# =============================================================================
# 报告生成
# =============================================================================

# 调用报告生成模块的辅助函数
call_report_generator() {
    local action="$1"
    shift

    if [[ -f "modules/report_generator.sh" ]]; then
        bash "modules/report_generator.sh" "$action" "$@"
    else
        log_error "报告生成模块不存在: modules/report_generator.sh"
        return 1
    fi
}

# 本地检测报告生成函数（简化版）
generate_local_report() {
    # 使用报告生成模块处理本地检测数据
    local temp_files_prefix="/tmp/afw3000"
    call_report_generator "process-local-device" "$temp_files_prefix" >> "$REPORT_FILE"

    # 清理临时文件
    rm -f "${temp_files_prefix}_hardware_result_localhost_$$" "${temp_files_prefix}_system_result_localhost_$$" "${temp_files_prefix}_software_result_localhost_$$" 2>/dev/null

}




generate_report() {
    # 确定检测模式
    local detection_mode
    if [[ "$LOCAL_MODE" == "true" ]]; then
        detection_mode="local"
    else
        detection_mode="remote"
    fi

    # 收集原始数据
    mkdir -p "$RAW_DATA_DIR"
    if [[ "$LOCAL_MODE" == "true" ]]; then
        local hostname=$(hostname)
        local local_ip=$(ifconfig | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}' 2>/dev/null || echo "127.0.0.1")
        call_raw_data_collector "all" "localhost" "$local_ip" "$(whoami)" "" "" "$RAW_DATA_DIR"
    else
        for node in $TARGET_NODES; do
            local node_config
            node_config=$(get_device_config "$node")
            local host user password port backup_user backup_password
            read -r host user password port backup_user backup_password <<<"$node_config"
            call_raw_data_collector "all" "$node" "$host" "$user" "$password" "$port" "$RAW_DATA_DIR"
        done
    fi

    # 使用报告生成模块生成完整报告
    REPORT_FILE=$(call_report_generator "full-report" "$detection_mode" "$TARGET_NODES" "$SCRIPT_VERSION" "$START_TIME" "$RAW_DATA_DIR" "$REPORT_DIR" "$REPORT_TAG")

    # 清理原始数据临时目录
    rm -rf "$RAW_DATA_DIR" 2>/dev/null

    log_info "检测报告已生成: $REPORT_FILE"

}


# =============================================================================
# 清理和退出
# =============================================================================

cleanup_and_exit() {
    local exit_code=$1

    log_info "清理检测环境..."

    # 清理SSH连接
    ssh_cleanup

    # 关闭调试模式
    set +x

    log_info "检测任务完成"

    exit $exit_code
}

# =============================================================================
# 主函数
# =============================================================================

main() {

    # 解析命令行参数
    parse_arguments "$@"

    # 初始化环境
    initialize_environment

    # 执行检测，生成报告
    if run_detection; then
        generate_report
        cleanup_and_exit 0
    else
        generate_report
        cleanup_and_exit 1
    fi
}

# =============================================================================
# 脚本入口点
# =============================================================================

# 设置错误处理
set -e
trap 'cleanup_and_exit 1' ERR INT TERM

# 调用主函数
main "$@"
