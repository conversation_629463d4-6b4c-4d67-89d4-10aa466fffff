# Shell处理JSON的最佳实践和注意事项

## 1. 工具选择原则

### 优先级排序
1. **jq** - 首选，功能最强大
2. **yq** - 需要处理多种格式时
3. **Python/Node.js一行命令** - 复杂逻辑处理
4. **传统工具组合** - 仅在简单场景且无法安装jq时使用

### 选择标准
```bash
# 简单字段提取 → jq
jq '.field' file.json

# 复杂数据转换 → jq
jq 'complex_transformation' file.json

# 多格式支持 → yq
yq '.field' file.yaml

# 复杂业务逻辑 → Python/Node.js
python3 -c "import json; ..."
```

## 2. 性能优化

### 大文件处理
```bash
# ❌ 错误：一次性加载大文件
jq '.users[]' huge_file.json

# ✅ 正确：流式处理
jq -c '.users[]' huge_file.json | while read -r line; do
    # 处理每一行
    echo "$line" | jq '.name'
done

# ✅ 更好：使用jq的流式API
jq --stream 'select(.[0][0] == "users")' huge_file.json
```

### 避免重复解析
```bash
# ❌ 错误：多次解析同一文件
name=$(jq -r '.name' data.json)
age=$(jq -r '.age' data.json)
email=$(jq -r '.email' data.json)

# ✅ 正确：一次解析，多次使用
eval $(jq -r '@sh "name=\(.name) age=\(.age) email=\(.email)"' data.json)
```

## 3. 错误处理

### 健壮的脚本编写
```bash
#!/bin/bash
set -euo pipefail  # 严格模式

# 检查文件存在性
if [[ ! -f "data.json" ]]; then
    echo "错误：文件不存在" >&2
    exit 1
fi

# 检查JSON格式
if ! jq empty data.json 2>/dev/null; then
    echo "错误：JSON格式无效" >&2
    exit 1
fi

# 安全的字段访问
value=$(jq -r '.field // "默认值"' data.json)

# 处理可能为null的值
jq -r 'if .field == null then "N/A" else .field end' data.json
```

### 常见错误处理模式
```bash
# 处理数组越界
jq -r '.users[10] // "用户不存在"' data.json

# 处理类型错误
jq -r 'if (.age | type) == "number" then .age else "年龄无效" end' data.json

# 使用try-catch
jq -r 'try .complex.nested.field catch "字段不存在"' data.json
```

## 4. 安全考虑

### 输入验证
```bash
# 验证JSON结构
validate_json_structure() {
    local file="$1"
    local required_fields=("name" "email" "age")
    
    for field in "${required_fields[@]}"; do
        if ! jq -e "has(\"$field\")" "$file" >/dev/null; then
            echo "错误：缺少必需字段 $field" >&2
            return 1
        fi
    done
}

# 数据类型验证
jq -e '
  (.name | type) == "string" and
  (.age | type) == "number" and  
  (.email | test("@"))
' data.json || echo "数据格式验证失败"
```

### 防止注入攻击
```bash
# ❌ 危险：直接拼接用户输入
user_input="malicious'; rm -rf /"
jq ".field = \"$user_input\"" data.json  # 危险！

# ✅ 安全：使用参数传递
jq --arg input "$user_input" '.field = $input' data.json
```

## 5. 代码组织

### 模块化函数
```bash
# 定义可重用的jq函数
get_user_summary() {
    local file="$1"
    local user_id="$2"
    
    jq --arg id "$user_id" '
        .users[] | 
        select(.id == ($id | tonumber)) |
        {name, email, age, active}
    ' "$file"
}

# 复杂查询的分步处理
process_user_data() {
    local file="$1"
    
    # 第一步：数据清洗
    jq 'del(.users[].internal_notes)' "$file" > temp1.json
    
    # 第二步：数据转换
    jq '.users |= map(. + {full_name: "\(.first_name) \(.last_name)"})' temp1.json > temp2.json
    
    # 第三步：最终输出
    jq '.users | sort_by(.age)' temp2.json
    
    # 清理临时文件
    rm -f temp1.json temp2.json
}
```

## 6. 调试技巧

### 调试复杂查询
```bash
# 使用debug函数
jq '.users[] | debug | select(.age > 30)' data.json

# 分步调试
jq '.users[]' data.json | head -1  # 查看单个记录结构
jq '.users[] | keys' data.json | head -1  # 查看字段名
jq '.users[] | type' data.json  # 查看数据类型

# 使用-v选项查看详细信息
jq -v '.complex_query' data.json
```

### 性能分析
```bash
# 测量执行时间
time jq '.complex_query' large_file.json

# 内存使用监控
/usr/bin/time -v jq '.query' file.json
```

## 7. 常见陷阱

### 字符串vs数字比较
```bash
# ❌ 错误：字符串比较
jq '.users[] | select(.age > "30")' data.json

# ✅ 正确：数字比较
jq '.users[] | select(.age > 30)' data.json
```

### 空值处理
```bash
# ❌ 可能出错：未处理null
jq '.users[].projects[].score | add' data.json

# ✅ 正确：过滤null值
jq '.users[].projects[].score | select(. != null) | add' data.json
```

### 数组索引
```bash
# ❌ 错误：假设数组有元素
jq '.users[0].name' data.json

# ✅ 正确：安全访问
jq '.users[0]?.name // "无用户"' data.json
```

## 8. 生产环境建议

### 日志记录
```bash
log_json_operation() {
    local operation="$1"
    local file="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] 执行JSON操作: $operation on $file" >> json_operations.log
}

# 使用示例
log_json_operation "extract_users" "data.json"
result=$(jq '.users[]' data.json)
```

### 配置管理
```bash
# 将复杂查询保存为文件
cat > user_summary.jq << 'EOF'
.users | map({
    name,
    email,
    age,
    location: .address.city,
    skill_count: (.skills | length)
}) | sort_by(.age)
EOF

# 使用配置文件
jq -f user_summary.jq data.json
```

### 监控和告警
```bash
# 检查JSON处理结果
check_json_result() {
    local result="$1"
    
    if [[ -z "$result" ]]; then
        echo "警告：JSON查询返回空结果" >&2
        return 1
    fi
    
    if ! echo "$result" | jq empty 2>/dev/null; then
        echo "错误：JSON查询返回无效JSON" >&2
        return 1
    fi
    
    return 0
}
```

## 9. 总结

### 核心原则
1. **优先使用专门工具**：jq > 传统工具组合
2. **注重错误处理**：验证输入，处理异常
3. **考虑性能**：大文件使用流式处理
4. **保证安全**：避免注入，验证数据
5. **代码可维护**：模块化，文档化

### 学习路径
1. 掌握jq基本语法
2. 学习常用内置函数
3. 练习复杂数据转换
4. 了解性能优化技巧
5. 掌握错误处理模式
